<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招标详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            width: 375px;
            height: 812px;
            overflow: hidden;
        }
        .phone-container {
            width: 375px;
            height: 812px;
            overflow-y: auto;
            position: relative;
            background: white;
        }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .mini-capsule {
            position: absolute;
            top: 8px;
            right: 12px;
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .content-section { border-bottom: 1px solid #f3f4f6; }
        .vip-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .vip-modal-content {
            background: white;
            border-radius: 20px;
            padding: 30px 20px;
            margin: 20px;
            text-align: center;
            max-width: 320px;
            width: 100%;
            animation: modalSlideIn 0.3s ease-out;
        }
        @keyframes modalSlideIn {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        .vip-crown {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 小程序胶囊按钮 -->
        <div class="mini-capsule">
            <i class="fas fa-ellipsis-h text-gray-600 text-xs"></i>
        </div>
        
        <!-- 顶部导航 -->
        <div class="gradient-bg px-4 pt-12 pb-4">
            <div class="flex items-center space-x-3">
                <button class="text-white">
                    <i class="fas fa-arrow-left text-lg"></i>
                </button>
                <h1 class="text-white text-lg font-semibold flex-1">招标详情</h1>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="bg-white flex-1 overflow-y-auto">
            <!-- 标题区域 -->
            <div class="px-4 py-4 content-section">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex space-x-2">
                        <span class="bg-red-100 text-red-600 px-2 py-1 rounded-md text-xs font-medium">紧急</span>
                        <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded-md text-xs font-medium">工程建设</span>
                    </div>
                    <span class="text-xs text-gray-400">2小时前发布</span>
                </div>
                <h2 class="text-lg font-bold text-gray-800 mb-3 leading-relaxed">某市政府办公大楼装修改造工程招标公告</h2>
                <div class="flex items-center space-x-4 text-sm text-gray-600">
                    <div class="flex items-center">
                        <i class="fas fa-eye w-4 mr-1"></i>
                        <span>1,234</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-heart w-4 mr-1"></i>
                        <span>56</span>
                    </div>
                </div>
            </div>

            <!-- 基本信息 -->
            <div class="px-4 py-4 content-section">
                <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                    基本信息
                </h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">项目编号</span>
                        <span class="text-sm font-medium text-gray-800">ZB2024001</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">招标方式</span>
                        <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs">公开招标</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">项目地点</span>
                        <span class="text-sm font-medium text-gray-800">北京市朝阳区</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">预算金额</span>
                        <span class="text-sm font-semibold text-red-600">500-800万元</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">报名截止</span>
                        <span class="text-sm font-medium text-gray-800">2024-01-15 17:00</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">开标时间</span>
                        <span class="text-sm font-medium text-gray-800">2024-01-20 09:00</span>
                    </div>
                </div>
            </div>

            <!-- 项目概况 -->
            <div class="px-4 py-4 content-section">
                <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-file-alt text-purple-500 mr-2"></i>
                    项目概况
                </h3>
                <div class="text-sm text-gray-700 leading-relaxed space-y-2">
                    <p>本项目为某市政府办公大楼装修改造工程，主要包括：</p>
                    <ul class="list-disc list-inside space-y-1 ml-2">
                        <li>办公区域装修改造约3000平方米</li>
                        <li>会议室及接待区域装修约500平方米</li>
                        <li>电气系统改造及智能化升级</li>
                        <li>消防系统改造及验收</li>
                        <li>环保材料使用，符合绿色建筑标准</li>
                    </ul>
                    <p class="mt-3">工期要求：90个工作日内完成</p>
                </div>
            </div>

            <!-- 投标要求 -->
            <div class="px-4 py-4 content-section">
                <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-clipboard-check text-orange-500 mr-2"></i>
                    投标要求
                </h3>
                <div class="text-sm text-gray-700 leading-relaxed space-y-2">
                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-3 rounded">
                        <p class="font-medium text-yellow-800 mb-1">资质要求：</p>
                        <ul class="list-disc list-inside space-y-1 text-yellow-700">
                            <li>建筑装修装饰工程专业承包一级资质</li>
                            <li>安全生产许可证</li>
                            <li>近三年无重大质量安全事故</li>
                        </ul>
                    </div>
                    <div class="bg-blue-50 border-l-4 border-blue-400 p-3 rounded">
                        <p class="font-medium text-blue-800 mb-1">业绩要求：</p>
                        <p class="text-blue-700">近五年内完成类似项目不少于3个，单项合同金额不低于300万元</p>
                    </div>
                </div>
            </div>

            <!-- 联系方式 -->
            <div class="px-4 py-4 content-section">
                <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-phone text-green-500 mr-2"></i>
                    联系方式
                </h3>
                <div class="space-y-3">
                    <div class="bg-gray-50 rounded-lg p-3">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-800">招标人</span>
                            <span class="text-sm text-gray-600">某市政府办公室</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm text-gray-600">联系人</span>
                            <span class="text-sm text-gray-800">张先生</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">联系电话</span>
                            <a href="tel:010-12345678" class="text-sm text-blue-600">010-12345678</a>
                        </div>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-3">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-800">代理机构</span>
                            <span class="text-sm text-gray-600">某招标代理有限公司</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">联系电话</span>
                            <a href="tel:010-87654321" class="text-sm text-blue-600">010-87654321</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 相关文档 -->
            <div class="px-4 py-4">
                <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-download text-indigo-500 mr-2"></i>
                    相关文档
                </h3>
                <div class="space-y-2">
                    <div class="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-file-pdf text-red-500"></i>
                            <span class="text-sm text-gray-800">招标文件.pdf</span>
                        </div>
                        <button class="text-blue-600 text-sm">下载</button>
                    </div>
                    <div class="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-file-word text-blue-500"></i>
                            <span class="text-sm text-gray-800">技术规格书.docx</span>
                        </div>
                        <button class="text-blue-600 text-sm">下载</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- VIP弹窗 -->
        <div id="vipModal" class="vip-modal" style="display: none;">
            <div class="vip-modal-content">
                <div class="vip-crown">
                    <i class="fas fa-crown text-white text-3xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-3">开通VIP会员</h3>
                <p class="text-gray-600 text-sm mb-6 leading-relaxed">
                    查看完整招标详情需要开通VIP会员<br>
                    解锁全部功能，获取更多商机
                </p>

                <!-- VIP特权展示 -->
                <div class="grid grid-cols-2 gap-3 mb-6">
                    <div class="bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-3 text-center">
                        <i class="fas fa-eye text-blue-500 text-lg mb-1"></i>
                        <p class="text-xs text-gray-700">查看详情</p>
                    </div>
                    <div class="bg-gradient-to-br from-green-50 to-blue-50 rounded-lg p-3 text-center">
                        <i class="fas fa-download text-green-500 text-lg mb-1"></i>
                        <p class="text-xs text-gray-700">下载文档</p>
                    </div>
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg p-3 text-center">
                        <i class="fas fa-bell text-purple-500 text-lg mb-1"></i>
                        <p class="text-xs text-gray-700">实时推送</p>
                    </div>
                    <div class="bg-gradient-to-br from-orange-50 to-red-50 rounded-lg p-3 text-center">
                        <i class="fas fa-search-plus text-orange-500 text-lg mb-1"></i>
                        <p class="text-xs text-gray-700">高级搜索</p>
                    </div>
                </div>

                <!-- 价格信息 -->
                <div class="bg-gradient-to-r from-purple-100 to-blue-100 rounded-lg p-4 mb-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">年度会员</p>
                            <p class="text-xs text-gray-500">最超值选择</p>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-purple-600">¥798</p>
                            <p class="text-xs text-gray-500 line-through">¥1188</p>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <button class="w-full bg-gradient-to-r from-purple-500 to-blue-500 text-white py-3 rounded-lg text-sm font-medium mb-3" onclick="goToMemberPage()">
                    立即开通VIP会员
                </button>
                <p class="text-xs text-gray-500">开通即可查看完整招标信息</p>
            </div>
        </div>

        <!-- 登录弹窗 -->
        <div id="loginModal" class="vip-modal" style="display: none;">
            <div class="vip-modal-content">
                <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-user text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-3">请先登录</h3>
                <p class="text-gray-600 text-sm mb-6 leading-relaxed">
                    登录后即可查看招标详情<br>
                    享受更多专业服务
                </p>

                <!-- 登录优势 -->
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 mb-6">
                    <div class="space-y-2">
                        <div class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>免费查看基础信息</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>保存浏览记录</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>个性化推荐</span>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <button class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-lg text-sm font-medium mb-3" onclick="goToLogin()">
                    立即登录
                </button>
                <p class="text-xs text-gray-500">登录即可享受更多服务</p>
            </div>
        </div>
    </div>

    <script>

        function showVipModal() {
            document.getElementById('vipModal').style.display = 'flex';
            // 禁止页面滚动
            document.body.style.overflow = 'hidden';
        }

        function showLoginModal() {
            document.getElementById('loginModal').style.display = 'flex';
            // 禁止页面滚动
            document.body.style.overflow = 'hidden';
        }

        function goToMemberPage() {
            // 跳转到会员开通页面
            window.location.href = 'member.html';
        }

        function goToLogin() {
            // 跳转到登录页面
            alert('跳转到登录页面');
            // window.location.href = 'login.html';
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            // 页面加载时检查用户状态
            checkUserStatusOnLoad();
        };

        // 页面加载时检查用户状态
        function checkUserStatusOnLoad() {
            const isLoggedIn = true; // 假设已登录（因为能进入详情页）
            const isVipMember = true; // 模拟非VIP用户

            if (!isLoggedIn) {
                // 未登录，显示登录弹窗
                showLoginModal();
            } else if (!isVipMember) {
                // 已登录但不是VIP会员，显示VIP弹窗
                showVipModal();
            }
            // 如果是VIP会员，则正常显示页面内容
        };

        // 阻止弹窗外部点击关闭
        document.getElementById('vipModal').addEventListener('click', function(e) {
            e.stopPropagation();
        });

        document.getElementById('loginModal').addEventListener('click', function(e) {
            e.stopPropagation();
        });
    </script>
</body>
</html>
