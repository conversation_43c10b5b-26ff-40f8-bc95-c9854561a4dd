# 热门搜索功能数据库操作指南

## 📋 概述

热门搜索功能需要数据库支持，用于存储和统计用户搜索行为，自动计算热门关键词和趋势。本指南详细说明了数据库的设计思路、操作流程和维护方法。

## 🗄️ 数据库结构

### 核心表结构

1. **`search_keywords`** - 搜索关键词主表
   - 存储关键词基本信息、搜索统计、趋势数据
   - 支持热门标记、新词标记、分类管理

2. **`search_records`** - 搜索记录表
   - 记录每次搜索行为的详细信息
   - 用于统计分析和用户行为追踪

3. **`keyword_categories`** - 关键词分类表
   - 管理关键词分类体系
   - 支持自动分类和手动分类

4. **`hot_search_config`** - 热门搜索配置表
   - 配置热门关键词的判断标准
   - 可动态调整算法参数

## 🔄 核心操作流程

### 1. 用户搜索时的数据处理

#### 触发时机
- 用户在搜索框输入关键词并搜索时
- 用户点击热门搜索标签时
- 用户点击搜索建议时

#### 操作步骤

```sql
-- 调用搜索记录存储过程
CALL RecordSearch(
    '智慧城市建设',           -- 搜索关键词
    1001,                    -- 用户ID（可为NULL）
    '*************',         -- 用户IP地址
    'Mozilla/5.0...',        -- 用户代理字符串
    25                       -- 搜索结果数量
);
```

#### 自动处理逻辑
1. **关键词检查**：查找关键词是否已存在
2. **自动创建**：不存在则创建新关键词记录
3. **自动分类**：使用规则引擎自动分类关键词
4. **统计更新**：更新搜索次数、日/周/月统计
5. **记录插入**：插入详细的搜索记录

### 2. 获取热门搜索数据

#### API接口实现

```sql
-- 获取热门搜索关键词（限制20个）
CALL GetHotSearchKeywords(20);
```

#### 返回数据格式
```json
[
    {
        "keyword": "智慧城市建设",
        "search_count": 1250,
        "trend": "up",
        "is_hot": true,
        "is_new": false
    },
    {
        "keyword": "人工智能",
        "search_count": 234,
        "trend": "up",
        "is_hot": false,
        "is_new": true
    }
]
```

#### 排序规则
1. 热门关键词优先显示
2. 按搜索次数降序排列
3. 新词次优先显示

### 3. 自动化维护任务

#### 每日维护（凌晨00:30执行）

```sql
-- 自动执行日常维护
CALL DailyReset();
```

**执行内容：**
- 保存昨日搜索数据用于趋势计算
- 重置日/周/月统计计数器
- 更新热门关键词状态
- 更新新词状态
- 计算趋势分数和方向

#### 每小时维护

```sql
-- 更新热门关键词状态
CALL UpdateHotKeywords();
```

**执行内容：**
- 根据最新搜索量重新计算热门关键词
- 确保热门列表的实时性

### 4. 趋势计算算法

#### 趋势分数计算公式
```
趋势分数 = ((今日搜索次数 - 昨日搜索次数) / 昨日搜索次数) × 100
```

#### 趋势状态判断
- **上升(up)**：趋势分数 > 10%
- **下降(down)**：趋势分数 < -10%
- **稳定(stable)**：-10% ≤ 趋势分数 ≤ 10%

#### 热门关键词判断标准
- 搜索次数 ≥ 配置的热门阈值（默认100次）
- 状态为活跃状态
- 按搜索次数排序取前N个（默认20个）

#### 新词判断标准
- 创建时间在配置天数内（默认7天）
- 状态为活跃状态
- 按搜索次数排序取前N个（默认10个）

## 🛠️ 手动维护操作

### 1. 关键词管理

#### 添加新分类
```sql
INSERT INTO keyword_categories (category_name, category_desc, sort_order) 
VALUES ('区块链技术', '区块链相关项目', 8);
```

#### 手动设置热门关键词
```sql
UPDATE search_keywords 
SET is_hot = TRUE 
WHERE keyword IN ('重点关键词1', '重点关键词2');
```

#### 禁用违规关键词
```sql
UPDATE search_keywords 
SET status = 'banned' 
WHERE keyword = '违规关键词';
```

### 2. 配置管理

#### 调整热门关键词阈值
```sql
UPDATE hot_search_config 
SET hot_threshold = 150,        -- 提高热门阈值
    max_hot_keywords = 25       -- 增加热门关键词数量
WHERE config_name = 'default_config';
```

#### 调整新词判断天数
```sql
UPDATE hot_search_config 
SET new_threshold_days = 10     -- 新词判断改为10天
WHERE config_name = 'default_config';
```

### 3. 数据清理

#### 清理过期搜索记录
```sql
-- 删除3个月前的搜索记录
DELETE FROM search_records 
WHERE search_time < DATE_SUB(NOW(), INTERVAL 3 MONTH);
```

#### 清理无效关键词
```sql
-- 删除30天内无搜索的关键词
DELETE FROM search_keywords 
WHERE search_count = 0 
AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

## 📊 数据分析查询

### 1. 搜索趋势分析

```sql
-- 查看最近7天的搜索趋势
SELECT 
    DATE(search_time) as date,
    COUNT(*) as total_searches,
    COUNT(DISTINCT keyword) as unique_keywords,
    COUNT(DISTINCT user_id) as unique_users
FROM search_records 
WHERE search_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
GROUP BY DATE(search_time)
ORDER BY date DESC;
```

### 2. 热门关键词排行

```sql
-- 查看本月热门关键词排行
SELECT 
    keyword,
    search_count,
    trend,
    category,
    last_search_time
FROM search_keywords 
WHERE monthly_search_count > 0
ORDER BY monthly_search_count DESC
LIMIT 50;
```

### 3. 用户搜索行为分析

```sql
-- 分析用户搜索偏好
SELECT 
    sk.category,
    COUNT(*) as search_count,
    COUNT(DISTINCT sr.user_id) as user_count,
    AVG(sr.result_count) as avg_results
FROM search_records sr
JOIN search_keywords sk ON sr.keyword_id = sk.id
WHERE sr.search_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY sk.category
ORDER BY search_count DESC;
```

## ⚡ 性能优化建议

### 1. 索引优化
- 确保所有查询都有合适的索引
- 定期分析慢查询并优化

### 2. 数据分区
- 对大表按时间进行分区
- 搜索记录表按月分区

### 3. 缓存策略
- 使用Redis缓存热门搜索结果
- 缓存时间设置为1小时

### 4. 定期维护
- 每周分析表性能
- 每月清理历史数据
- 监控数据库性能指标

## 🚨 注意事项

1. **数据一致性**：确保搜索记录和统计数据的一致性
2. **性能监控**：关注搜索记录表的增长速度
3. **存储空间**：定期清理历史数据避免存储空间不足
4. **安全性**：对搜索内容进行过滤，防止恶意搜索
5. **隐私保护**：合理处理用户搜索数据，遵守隐私法规
