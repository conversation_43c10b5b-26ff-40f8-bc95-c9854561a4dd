<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招标信息列表</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            width: 375px;
            height: 812px;
            overflow: hidden;
        }
        .phone-container {
            width: 375px;
            height: 812px;
            overflow-y: auto;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .mini-capsule {
            position: absolute;
            top: 8px;
            right: 12px;
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 小程序胶囊按钮 -->
        <div class="mini-capsule">
            <i class="fas fa-ellipsis-h text-gray-600 text-xs"></i>
        </div>
        
        <!-- 顶部搜索区域 -->
        <div class="gradient-bg px-4 pt-12 pb-6">
            <div class="flex items-center space-x-3 mb-4">
                <div class="flex-1 relative">
                    <input type="text" placeholder="搜索招标信息..." 
                           class="w-full bg-white/90 backdrop-blur-sm rounded-full px-4 py-3 pl-10 text-sm placeholder-gray-500 border-0 focus:outline-none focus:ring-2 focus:ring-white/50">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm"></i>
                </div>
            </div>
            
            <!-- 分类标签 -->
            <div class="flex space-x-2 overflow-x-auto pb-2">
                <span class="bg-white/90 text-purple-600 px-4 py-2 rounded-full text-xs font-medium whitespace-nowrap">全部</span>
                <span class="bg-white/20 text-white px-4 py-2 rounded-full text-xs whitespace-nowrap">工程建设</span>
                <span class="bg-white/20 text-white px-4 py-2 rounded-full text-xs whitespace-nowrap">政府采购</span>
                <span class="bg-white/20 text-white px-4 py-2 rounded-full text-xs whitespace-nowrap">设备采购</span>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="bg-white flex-1 overflow-y-auto">
            <!-- 统计信息 -->
            <div class="px-4 py-3 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-100">
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">今日新增 <span class="text-blue-600 font-semibold">128</span> 条</span>
                    <span class="text-sm text-gray-500">共 <span class="font-semibold">2,456</span> 条信息</span>
                </div>
            </div>

            <!-- 招标信息列表 -->
            <div class="px-4 py-2 space-y-3">
                <!-- 招标信息卡片1 -->
                <div class="bg-white rounded-xl p-4 card-shadow border border-gray-100">
                    <div class="flex items-start justify-between mb-2">
                        <span class="bg-red-100 text-red-600 px-2 py-1 rounded-md text-xs font-medium">紧急</span>
                        <span class="text-xs text-gray-400">2小时前</span>
                    </div>
                    <h3 class="text-sm font-semibold text-gray-800 mb-2 line-clamp-2">某市政府办公大楼装修改造工程招标公告</h3>
                    <div class="space-y-1 mb-3">
                        <div class="flex items-center text-xs text-gray-600">
                            <i class="fas fa-map-marker-alt w-4"></i>
                            <span>北京市朝阳区</span>
                        </div>
                        <div class="flex items-center text-xs text-gray-600">
                            <i class="fas fa-coins w-4"></i>
                            <span>预算金额：500-800万元</span>
                        </div>
                        <div class="flex items-center text-xs text-gray-600">
                            <i class="fas fa-calendar w-4"></i>
                            <span>截止时间：2024-01-15</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded text-xs">工程建设</span>
                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs">公开招标</span>
                        </div>
                        <button class="text-purple-600 text-xs font-medium" onclick="checkDetailAccess()">查看详情</button>
                    </div>
                </div>

                <!-- 招标信息卡片2 -->
                <div class="bg-white rounded-xl p-4 card-shadow border border-gray-100">
                    <div class="flex items-start justify-between mb-2">
                        <span class="bg-orange-100 text-orange-600 px-2 py-1 rounded-md text-xs font-medium">热门</span>
                        <span class="text-xs text-gray-400">5小时前</span>
                    </div>
                    <h3 class="text-sm font-semibold text-gray-800 mb-2 line-clamp-2">智慧城市信息化系统建设项目采购公告</h3>
                    <div class="space-y-1 mb-3">
                        <div class="flex items-center text-xs text-gray-600">
                            <i class="fas fa-map-marker-alt w-4"></i>
                            <span>上海市浦东新区</span>
                        </div>
                        <div class="flex items-center text-xs text-gray-600">
                            <i class="fas fa-coins w-4"></i>
                            <span>预算金额：1000-1500万元</span>
                        </div>
                        <div class="flex items-center text-xs text-gray-600">
                            <i class="fas fa-calendar w-4"></i>
                            <span>截止时间：2024-01-20</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="bg-purple-100 text-purple-600 px-2 py-1 rounded text-xs">信息化</span>
                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs">公开招标</span>
                        </div>
                        <button class="text-purple-600 text-xs font-medium" onclick="checkDetailAccess()">查看详情</button>
                    </div>
                </div>

                <!-- 招标信息卡片3 -->
                <div class="bg-white rounded-xl p-4 card-shadow border border-gray-100">
                    <div class="flex items-start justify-between mb-2">
                        <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded-md text-xs font-medium">新发布</span>
                        <span class="text-xs text-gray-400">1天前</span>
                    </div>
                    <h3 class="text-sm font-semibold text-gray-800 mb-2 line-clamp-2">医疗设备采购项目招标公告</h3>
                    <div class="space-y-1 mb-3">
                        <div class="flex items-center text-xs text-gray-600">
                            <i class="fas fa-map-marker-alt w-4"></i>
                            <span>广州市天河区</span>
                        </div>
                        <div class="flex items-center text-xs text-gray-600">
                            <i class="fas fa-coins w-4"></i>
                            <span>预算金额：300-500万元</span>
                        </div>
                        <div class="flex items-center text-xs text-gray-600">
                            <i class="fas fa-calendar w-4"></i>
                            <span>截止时间：2024-01-25</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <span class="bg-pink-100 text-pink-600 px-2 py-1 rounded text-xs">医疗设备</span>
                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs">公开招标</span>
                        </div>
                        <button class="text-purple-600 text-xs font-medium" onclick="checkDetailAccess()">查看详情</button>
                    </div>
                </div>
            </div>

            <!-- 加载更多 -->
            <div class="px-4 py-6 text-center">
                <button class="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-2 rounded-full text-sm font-medium">
                    加载更多
                </button>
            </div>
        </div>


    </div>

    <script>
        // 验证用户访问详情的权限
        function checkDetailAccess() {
            // 模拟用户状态检查
            const isLoggedIn = true; // 可以从后端获取或本地存储获取
            const isVipMember = false; // 可以从后端获取或本地存储获取

            if (!isLoggedIn) {
                // 未登录，显示去登录
                alert('请先登录后查看详情');
                // 这里可以跳转到登录页面
                // window.location.href = 'login.html';
            } else {
                // 已登录，验证是否购买会员
                if (isVipMember) {
                    // 已是会员，正常显示详情
                    window.location.href = 'detail.html';
                } else {
                    // 不是会员，显示购买会员弹窗
                    alert('查看完整详情需要开通VIP会员');
                    window.location.href = 'member.html';
                }
            }
        }
    </script>
</body>
</html>
