<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开通会员</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #F5F7FA;
            color: #333;
            max-width: 375px;
            margin: 0 auto;
            height: 812px;
            position: relative;
            overflow: hidden;
            border: 1px solid #ddd;
            border-radius: 30px;
        }
        
        .close-button {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 20px;
            height: 20px;
            background-color: #000;
            opacity: 0.5;
            border-radius: 50%;
            z-index: 100;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #F59E0B 0%, #F97316 100%);
        }
        
        /* 隐藏滚动条但保留功能 */
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .plan-card {
            transition: all 0.3s;
            border: 2px solid transparent;
        }
        
        .plan-card.selected {
            border-color: #F59E0B;
            background-color: #FFFBEB;
        }
        
        .plan-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .payment-option {
            transition: all 0.2s;
        }
        
        .payment-option.selected {
            border-color: #F59E0B;
            background-color: #FFFBEB;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .feature-item i {
            color: #F59E0B;
            margin-right: 8px;
            flex-shrink: 0;
        }
    </style>
</head>
<body class="flex flex-col">
    <!-- 小程序右上角胶囊按钮 -->
    <div class="close-button"></div>
    
    <!-- 头部 -->
    <header class="gradient-bg p-4 pt-10 pb-6">
        <div class="flex items-center">
            <a href="list.html" class="text-white mr-3">
                <i class="ri-arrow-left-line text-xl"></i>
            </a>
            <h1 class="text-lg font-bold text-white">开通会员</h1>
        </div>
    </header>
    
    <!-- 内容区域 -->
    <main class="flex-1 overflow-y-auto hide-scrollbar bg-gray-50">
        <!-- 会员特权 -->
        <section class="bg-white p-5">
            <h2 class="text-lg font-bold mb-4">会员特权</h2>
            
            <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="feature-item">
                    <i class="ri-vip-crown-fill"></i>
                    <span class="text-sm">VIP专属招标信息</span>
                </div>
                <div class="feature-item">
                    <i class="ri-phone-fill"></i>
                    <span class="text-sm">查看招标方联系方式</span>
                </div>
                <div class="feature-item">
                    <i class="ri-file-download-fill"></i>
                    <span class="text-sm">招标文件下载</span>
                </div>
                <div class="feature-item">
                    <i class="ri-notification-badge-fill"></i>
                    <span class="text-sm">项目提醒推送</span>
                </div>
                <div class="feature-item">
                    <i class="ri-customer-service-2-fill"></i>
                    <span class="text-sm">专属客服服务</span>
                </div>
                <div class="feature-item">
                    <i class="ri-search-eye-fill"></i>
                    <span class="text-sm">高级搜索功能</span>
                </div>
            </div>
        </section>
        
        <!-- 会员套餐 -->
        <section class="bg-white mt-2 p-5">
            <h2 class="text-lg font-bold mb-4">选择套餐</h2>
            
            <div class="space-y-4">
                <div class="plan-card selected rounded-xl p-4 relative">
                    <div class="absolute top-3 right-3 h-5 w-5 bg-yellow-500 rounded-full flex items-center justify-center">
                        <i class="ri-check-line text-white text-xs"></i>
                    </div>
                    <div class="flex items-center mb-2">
                        <h3 class="text-base font-bold">年度会员</h3>
                        <span class="ml-2 text-xs bg-red-500 text-white px-2 py-0.5 rounded">推荐</span>
                    </div>
                    <div class="flex items-baseline mb-2">
                        <span class="text-2xl font-bold text-yellow-500">¥1998</span>
                        <span class="text-sm text-gray-500 line-through ml-2">¥2988</span>
                        <span class="text-xs text-red-500 ml-2">省990元</span>
                    </div>
                    <p class="text-xs text-gray-500">每天仅需5.5元，畅享全部会员特权</p>
                </div>
                
                <div class="plan-card rounded-xl p-4 border-2 border-gray-200 relative">
                    <div class="flex items-center mb-2">
                        <h3 class="text-base font-bold">季度会员</h3>
                    </div>
                    <div class="flex items-baseline mb-2">
                        <span class="text-2xl font-bold text-gray-700">¥698</span>
                        <span class="text-sm text-gray-500 line-through ml-2">¥898</span>
                        <span class="text-xs text-red-500 ml-2">省200元</span>
                    </div>
                    <p class="text-xs text-gray-500">每天仅需7.8元，享受3个月会员特权</p>
                </div>
                
                <div class="plan-card rounded-xl p-4 border-2 border-gray-200 relative">
                    <div class="flex items-center mb-2">
                        <h3 class="text-base font-bold">月度会员</h3>
                    </div>
                    <div class="flex items-baseline mb-2">
                        <span class="text-2xl font-bold text-gray-700">¥298</span>
                        <span class="text-sm text-gray-500 line-through ml-2">¥398</span>
                        <span class="text-xs text-red-500 ml-2">省100元</span>
                    </div>
                    <p class="text-xs text-gray-500">每天仅需9.9元，体验1个月会员特权</p>
                </div>
            </div>
        </section>
        
        <!-- 支付方式 -->
        <section class="bg-white mt-2 p-5 mb-24">
            <h2 class="text-lg font-bold mb-4">支付方式</h2>
            
            <div class="space-y-3">
                <div class="payment-option selected flex items-center justify-between p-3 border rounded-lg">
                    <div class="flex items-center">
                        <img src="https://cdn.jsdelivr.net/gh/Lete114/CDN/svg/WeChat.svg" alt="微信支付" class="w-6 h-6 mr-3">
                        <span>微信支付</span>
                    </div>
                    <div class="h-5 w-5 bg-yellow-500 rounded-full flex items-center justify-center">
                        <i class="ri-check-line text-white text-xs"></i>
                    </div>
                </div>
                
                <div class="payment-option flex items-center justify-between p-3 border rounded-lg">
                    <div class="flex items-center">
                        <img src="https://cdn.jsdelivr.net/gh/Lete114/CDN/svg/Alipay.svg" alt="支付宝" class="w-6 h-6 mr-3">
                        <span>支付宝</span>
                    </div>
                    <div class="h-5 w-5 border border-gray-300 rounded-full"></div>
                </div>
                
                <div class="payment-option flex items-center justify-between p-3 border rounded-lg">
                    <div class="flex items-center">
                        <i class="ri-bank-card-line text-blue-500 text-xl mr-3"></i>
                        <span>银行卡支付</span>
                    </div>
                    <div class="h-5 w-5 border border-gray-300 rounded-full"></div>
                </div>
            </div>
            
            <div class="mt-4 text-xs text-gray-500 flex items-center">
                <input type="checkbox" id="agreement" checked class="mr-2">
                <label for="agreement">我已阅读并同意<a href="#" class="text-blue-500">《会员服务协议》</a></label>
            </div>
        </section>
    </main>
    
    <!-- 底部支付按钮 -->
    <footer class="bg-white border-t border-gray-200 p-4 fixed bottom-0 w-full max-w-[375px] shadow-lg">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-500">实付金额</p>
                <p class="text-xl font-bold text-yellow-500">¥1998</p>
            </div>
            <button class="gradient-bg text-white px-8 py-3 rounded-full font-medium">
                立即开通
            </button>
        </div>
    </footer>
</body>
</html>