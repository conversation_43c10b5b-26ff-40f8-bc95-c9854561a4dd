<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开通会员</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            width: 375px;
            height: 812px;
            overflow: hidden;
        }
        .phone-container {
            width: 375px;
            height: 812px;
            overflow-y: auto;
            position: relative;
            background: white;
        }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .gold-gradient { background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .mini-capsule {
            position: absolute;
            top: 8px;
            right: 12px;
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .price-card { transition: all 0.3s ease; }
        .price-card:hover { transform: translateY(-2px); }
        .price-card.selected { border-color: #8b5cf6; box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2); }
        .city-option { transition: all 0.2s ease; }
        .city-option:hover { transform: translateY(-1px); }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 小程序胶囊按钮 -->
        <div class="mini-capsule">
            <i class="fas fa-ellipsis-h text-gray-600 text-xs"></i>
        </div>
        
        <!-- 顶部区域 -->
        <div class="gradient-bg px-4 pt-12 pb-8">
            <div class="flex items-center space-x-3 mb-6">
                <button class="text-white">
                    <i class="fas fa-arrow-left text-lg"></i>
                </button>
                <h1 class="text-white text-lg font-semibold flex-1">开通会员</h1>
            </div>
            
            <!-- 会员特权展示 -->
            <div class="text-center text-white">
                <div class="w-20 h-20 gold-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-crown text-white text-2xl"></i>
                </div>
                <h2 class="text-xl font-bold mb-2">招标信息VIP会员</h2>
                <p class="text-white/80 text-sm">解锁全部功能，获取更多商机</p>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="bg-white flex-1 overflow-y-auto">
            <!-- 会员权益 -->
            <div class="px-4 py-6">
                <h3 class="text-lg font-bold text-gray-800 mb-4 text-center">会员专享权益</h3>
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <div class="bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl p-4 text-center">
                        <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-search-plus text-white"></i>
                        </div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-1">高级搜索</h4>
                        <p class="text-xs text-gray-600">多维度筛选，精准匹配</p>
                    </div>
                    <div class="bg-gradient-to-br from-green-50 to-blue-50 rounded-xl p-4 text-center">
                        <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-bell text-white"></i>
                        </div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-1">实时推送</h4>
                        <p class="text-xs text-gray-600">第一时间获取信息</p>
                    </div>
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-4 text-center">
                        <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-download text-white"></i>
                        </div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-1">文档下载</h4>
                        <p class="text-xs text-gray-600">无限制下载招标文件</p>
                    </div>
                    <div class="bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-4 text-center">
                        <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-chart-line text-white"></i>
                        </div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-1">数据分析</h4>
                        <p class="text-xs text-gray-600">行业趋势深度分析</p>
                    </div>
                </div>

                <!-- 城市选择 -->
                <h3 class="text-lg font-bold text-gray-800 mb-4 text-center">选择城市</h3>
                <div class="mb-6">
                    <div class="grid grid-cols-3 gap-3 mb-4">
                        <button class="city-option bg-purple-100 border-2 border-purple-500 text-purple-600 py-3 rounded-lg text-sm font-medium">
                            北京
                        </button>
                        <button class="city-option bg-white border-2 border-gray-200 text-gray-600 py-3 rounded-lg text-sm font-medium">
                            上海
                        </button>
                        <button class="city-option bg-white border-2 border-gray-200 text-gray-600 py-3 rounded-lg text-sm font-medium">
                            广州
                        </button>
                        <button class="city-option bg-white border-2 border-gray-200 text-gray-600 py-3 rounded-lg text-sm font-medium">
                            深圳
                        </button>
                        <button class="city-option bg-white border-2 border-gray-200 text-gray-600 py-3 rounded-lg text-sm font-medium">
                            杭州
                        </button>
                        <button class="city-option bg-white border-2 border-gray-200 text-gray-600 py-3 rounded-lg text-sm font-medium">
                            成都
                        </button>
                        <button class="city-option bg-white border-2 border-gray-200 text-gray-600 py-3 rounded-lg text-sm font-medium">
                            武汉
                        </button>
                        <button class="city-option bg-white border-2 border-gray-200 text-gray-600 py-3 rounded-lg text-sm font-medium">
                            西安
                        </button>
                        <button class="city-option bg-white border-2 border-gray-200 text-gray-600 py-3 rounded-lg text-sm font-medium">
                            内蒙古
                        </button>
                    </div>
                    <div class="bg-blue-50 border-l-4 border-blue-400 p-3 rounded">
                        <p class="text-sm text-blue-700">
                            <i class="fas fa-info-circle mr-2"></i>
                            已选择：<span class="font-semibold">北京</span> 地区的招标信息服务
                        </p>
                    </div>
                </div>

                <!-- 价格方案 -->
                <h3 class="text-lg font-bold text-gray-800 mb-4 text-center">选择套餐</h3>
                <div class="space-y-3 mb-6">
                    <!-- 月度会员 -->
                    <div class="price-card bg-white border-2 border-gray-200 rounded-xl p-4 cursor-pointer">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-6 h-6 border-2 border-gray-300 rounded-full flex items-center justify-center">
                                    <div class="w-3 h-3 bg-gray-300 rounded-full hidden"></div>
                                </div>
                                <div>
                                    <h4 class="text-sm font-semibold text-gray-800">月度会员</h4>
                                    <p class="text-xs text-gray-600">适合短期使用</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-gray-800">¥99</div>
                                <div class="text-xs text-gray-500">每月</div>
                            </div>
                        </div>
                    </div>

                    <!-- 季度会员 -->
                    <div class="price-card bg-white border-2 border-gray-200 rounded-xl p-4 cursor-pointer">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-6 h-6 border-2 border-gray-300 rounded-full flex items-center justify-center">
                                    <div class="w-3 h-3 bg-gray-300 rounded-full hidden"></div>
                                </div>
                                <div>
                                    <h4 class="text-sm font-semibold text-gray-800">季度会员</h4>
                                    <p class="text-xs text-gray-600">性价比之选</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="flex items-center space-x-2">
                                    <span class="text-xs text-gray-400 line-through">¥297</span>
                                    <span class="bg-red-100 text-red-600 px-2 py-1 rounded text-xs">省¥48</span>
                                </div>
                                <div class="text-lg font-bold text-gray-800">¥249</div>
                                <div class="text-xs text-gray-500">3个月</div>
                            </div>
                        </div>
                    </div>

                    <!-- 年度会员 - 推荐 -->
                    <div class="price-card selected bg-white border-2 border-purple-500 rounded-xl p-4 cursor-pointer relative">
                        <div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
                            <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-medium">推荐</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-6 h-6 border-2 border-purple-500 rounded-full flex items-center justify-center">
                                    <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                                </div>
                                <div>
                                    <h4 class="text-sm font-semibold text-gray-800">年度会员</h4>
                                    <p class="text-xs text-gray-600">最超值选择</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="flex items-center space-x-2">
                                    <span class="text-xs text-gray-400 line-through">¥1188</span>
                                    <span class="bg-red-100 text-red-600 px-2 py-1 rounded text-xs">省¥390</span>
                                </div>
                                <div class="text-lg font-bold text-purple-600">¥798</div>
                                <div class="text-xs text-gray-500">12个月</div>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- 支付方式 -->
                <div class="mb-6">
                    <h4 class="text-sm font-semibold text-gray-800 mb-3">支付方式</h4>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between bg-white border border-gray-200 rounded-lg p-3">
                            <div class="flex items-center space-x-3">
                                <div class="w-6 h-6 border-2 border-purple-500 rounded-full flex items-center justify-center">
                                    <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                                </div>
                                <i class="fab fa-weixin text-green-500 text-lg"></i>
                                <span class="text-sm text-gray-800">微信支付</span>
                            </div>
                            <span class="text-xs text-green-600">推荐</span>
                        </div>
                        <div class="flex items-center justify-between bg-white border border-gray-200 rounded-lg p-3">
                            <div class="flex items-center space-x-3">
                                <div class="w-6 h-6 border-2 border-gray-300 rounded-full flex items-center justify-center">
                                    <div class="w-3 h-3 bg-gray-300 rounded-full hidden"></div>
                                </div>
                                <i class="fab fa-alipay text-blue-500 text-lg"></i>
                                <span class="text-sm text-gray-800">支付宝</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部支付按钮 -->
        <div class="bg-white border-t border-gray-200 px-4 py-4">
            <div class="flex items-center justify-between mb-3">
                <span class="text-sm text-gray-600">应付金额</span>
                <span class="text-lg font-bold text-purple-600">¥798</span>
            </div>
            <button class="w-full bg-gradient-to-r from-purple-500 to-blue-500 text-white py-3 rounded-lg text-sm font-medium">
                立即开通会员
            </button>
            <p class="text-xs text-gray-500 text-center mt-2">开通即表示同意《会员服务协议》</p>
        </div>
    </div>
</body>
</html>
