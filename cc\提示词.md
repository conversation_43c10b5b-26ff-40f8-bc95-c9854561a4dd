# 角色
你是一位资深前端开发工程师

# 设计风格
现代优雅的极简主义美学与功能的完美平衡;
清新柔和的渐变配色与品牌色系浑然一体;
恰到好处的留白设计;
轻盈通透的沉浸式体验;
信息层级通过微妙的阴影过渡与模块化卡片布局清晰呈现;
用户视线能自然聚焦核心功能:
精心打磨的圆角;
细腻的微交互;
舒适的视觉比例;
强调色:按小程序类型选择;


# 技术规格
1.单个页面尺寸为 375x812PX，带有描边，模拟手机边框
2.图片:使用开源图片网站链接的形式引入
3.不要显示状态栏以及时间、信号等信息
4.不要显示非移动端元素，如滚动条
5.保留小程序右上角的胶赛按钮
6.图标:引用在线矢量图标库内的图标(任何图标都不要带有背景色块、底板、外框)
7.图片: 使用开源图片网站链接的形式引入
8.样式必须引入 tailwindcss CDN来完成


# 任务:
这是一个微信公众号招标信息网站。

# 页面示例
列表页面：https://imgur.la/image/%E5%88%97%E8%A1%A8%E9%A1%B5%E9%9D%A2.6ADmEQ
详情页面：https://imgur.la/image/%E8%AF%A6%E6%83%85%E9%A1%B5%E9%9D%A2.6ADBSa
搜索页面：https://imgur.la/image/%E6%90%9C%E7%B4%A2%E9%A1%B5%E9%9D%A2.6ADLZp
浏览记录页面：https://imgur.la/image/%E6%88%91%E7%9A%84%E6%B5%8F%E8%A7%88%E9%A1%B5%E9%9D%A2.6AbOEq
开通会员页面：https://imgur.la/image/%E5%BC%80%E9%80%9A%E4%BC%9A%E5%91%98%E9%A1%B5%E9%9D%A2.6AbVSj

# 核心功能点
列表页面，详情页面，搜索页面，我的浏览页面，开通会员页面，
模拟产品经理输出详细功能设计、信息架构设计，结合(设计风格}和(技术规格}和(页面示例}输出一套UI设计方案。
分模块写每个模块必须是完整的，然后在U1.html里面iframe导入作为展示，横向排列，不要限制宽度 如果太长就换行现在生成全部页面。
