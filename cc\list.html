<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招标信息列表</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #F5F7FA;
            color: #333;
            max-width: 375px;
            margin: 0 auto;
            height: 812px;
            position: relative;
            overflow: hidden;
            border: 1px solid #ddd;
            border-radius: 30px;
        }
        
        .close-button {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 20px;
            height: 20px;
            background-color: #000;
            opacity: 0.5;
            border-radius: 50%;
            z-index: 100;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #1D4ED8 0%, #3B82F6 100%);
        }
        
        .card {
            transition: transform 0.2s, box-shadow 0.2s;
            border-radius: 12px;
        }
        
        .card:active {
            transform: scale(0.98);
        }
        
        .tab.active {
            color: #3B82F6;
            border-bottom: 2px solid #3B82F6;
            font-weight: 500;
        }
        
        /* 隐藏滚动条但保留功能 */
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        /* 新增样式 */
        .category-tag {
            transition: all 0.2s ease;
        }
        
        .category-tag:hover {
            transform: translateY(-2px);
        }
        
        .vip-badge {
            background: linear-gradient(135deg, #F59E0B 0%, #F97316 100%);
        }
    </style>
</head>
<body class="flex flex-col">
    <!-- 小程序右上角胶囊按钮 -->
    <div class="close-button"></div>
    
    <!-- 头部区域 -->
    <header class="gradient-bg p-4 pt-10 pb-6 rounded-b-3xl shadow-md">
        <div class="flex items-center justify-between">
            <h1 class="text-xl font-bold text-white">招标信息</h1>
        </div>
    </header>
    
    <!-- 分类标签 -->
    <div class="px-4 pt-4 pb-2 bg-white sticky top-0 z-10 shadow-sm">
        <div class="flex space-x-6 overflow-x-auto hide-scrollbar">
            <button class="tab active whitespace-nowrap pb-2 font-medium">全部</button>
            <button class="tab whitespace-nowrap pb-2 text-gray-500">工程建设</button>
            <button class="tab whitespace-nowrap pb-2 text-gray-500">政府采购</button>
            <button class="tab whitespace-nowrap pb-2 text-gray-500">医疗器械</button>
            <button class="tab whitespace-nowrap pb-2 text-gray-500">企业采购</button>
            <button class="tab whitespace-nowrap pb-2 text-gray-500">PPP项目</button>
        </div>
    </div>
    
    <!-- 列表内容 -->
    <main class="flex-1 overflow-y-auto hide-scrollbar bg-gray-50 p-4">
        <!-- 推荐项目 -->
        <div class="mb-6">
            <h2 class="text-lg font-bold mb-3 flex items-center">
                <div class="w-1 h-5 bg-blue-500 rounded-full mr-2"></div>
                推荐项目
                <a href="#" class="text-blue-500 text-sm ml-auto flex items-center">更多 <i class="ri-arrow-right-s-line"></i></a>
            </h2>
            <div class="card bg-white rounded-xl shadow-sm hover:shadow-md p-4 mb-4">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="bg-blue-50 text-blue-600 text-xs px-2 py-1 rounded-full inline-block mb-2 font-medium">工程建设</div>
                        <h3 class="font-medium text-gray-900 mb-1 line-clamp-2">杭州市某区道路改造工程施工招标公告</h3>
                        <p class="text-gray-500 text-sm mb-2 line-clamp-2">本项目为杭州市某区道路改造工程，包含道路拓宽、路面修复、交通设施更新等内容...</p>
                        <div class="flex items-center text-xs text-gray-400">
                            <span class="flex items-center"><i class="ri-money-cny-circle-line mr-1"></i>预算：¥1200万</span>
                            <span class="mx-2">|</span>
                            <span class="flex items-center"><i class="ri-time-line mr-1"></i>截止：2023-06-30</span>
                        </div>
                    </div>
                    <div class="bg-yellow-500 text-white text-xs px-2 py-1 rounded-md ml-2">
                        VIP
                    </div>
                </div>
            </div>
            
            <div class="card bg-white rounded-xl shadow-sm hover:shadow-md p-4">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="bg-green-50 text-green-600 text-xs px-2 py-1 rounded-full inline-block mb-2 font-medium">政府采购</div>
                        <h3 class="font-medium text-gray-900 mb-1 line-clamp-2">2023年市政府办公设备采购项目招标公告</h3>
                        <p class="text-gray-500 text-sm mb-2 line-clamp-2">本项目为市政府办公设备采购项目，包含电脑、打印机、会议系统等设备的采购与安装...</p>
                        <div class="flex items-center text-xs text-gray-400">
                            <span class="flex items-center"><i class="ri-money-cny-circle-line mr-1"></i>预算：¥350万</span>
                            <span class="mx-2">|</span>
                            <span class="flex items-center"><i class="ri-time-line mr-1"></i>截止：2023-07-15</span>
                        </div>
                    </div>
                    <div class="bg-yellow-500 text-white text-xs px-2 py-1 rounded-md ml-2">
                        VIP
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 最新项目 -->
        <div>
            <h2 class="text-lg font-bold mb-3 flex items-center">
                <div class="w-1 h-5 bg-blue-500 rounded-full mr-2"></div>
                最新项目
                <a href="#" class="text-blue-500 text-sm ml-auto flex items-center">更多 <i class="ri-arrow-right-s-line"></i></a>
            </h2>
            
            <a href="detail.html" class="card bg-white rounded-xl shadow-sm hover:shadow-md p-4 mb-4 block">
                <div class="bg-blue-50 text-blue-600 text-xs px-2 py-1 rounded-full inline-block mb-2 font-medium">医疗器械</div>
                <h3 class="font-medium text-gray-900 mb-1 line-clamp-2">市第一人民医院医疗设备采购项目</h3>
                <p class="text-gray-500 text-sm mb-2 line-clamp-2">采购CT扫描仪、核磁共振成像系统等大型医疗设备，预算2000万元...</p>
                <div class="flex items-center text-xs text-gray-400">
                    <span>预算：¥2000万</span>
                    <span class="mx-2">|</span>
                    <span>截止：2023-08-10</span>
                </div>
            </a>
            
            <div class="card bg-white rounded-xl shadow-sm hover:shadow-md p-4 mb-4">
                <div class="bg-blue-50 text-blue-600 text-xs px-2 py-1 rounded-full inline-block mb-2 font-medium">工程建设</div>
                <h3 class="font-medium text-gray-900 mb-1 line-clamp-2">城市公园绿化改造工程招标公告</h3>
                <p class="text-gray-500 text-sm mb-2 line-clamp-2">对市中心公园进行绿化改造，包括植被更新、景观设计、灌溉系统安装等...</p>
                <div class="flex items-center text-xs text-gray-400">
                    <span>预算：¥800万</span>
                    <span class="mx-2">|</span>
                    <span>截止：2023-07-25</span>
                </div>
            </div>
            
            <div class="card bg-white rounded-xl shadow-sm hover:shadow-md p-4 mb-4">
                <div class="bg-orange-50 text-orange-600 text-xs px-2 py-1 rounded-full inline-block mb-2 font-medium">企业采购</div>
                <h3 class="font-medium text-gray-900 mb-1 line-clamp-2">某科技公司数据中心建设项目招标</h3>
                <p class="text-gray-500 text-sm mb-2 line-clamp-2">建设高性能数据中心，包括服务器、存储设备、网络设备采购及安装调试...</p>
                <div class="flex items-center text-xs text-gray-400">
                    <span>预算：¥1500万</span>
                    <span class="mx-2">|</span>
                    <span>截止：2023-09-01</span>
                </div>
            </div>
            
            <div class="card bg-white rounded-xl shadow-sm hover:shadow-md p-4">
                <div class="bg-green-50 text-green-600 text-xs px-2 py-1 rounded-full inline-block mb-2 font-medium">政府采购</div>
                <h3 class="font-medium text-gray-900 mb-1 line-clamp-2">市教育局2023年教学设备采购项目</h3>
                <p class="text-gray-500 text-sm mb-2 line-clamp-2">为全市中小学采购智能黑板、多媒体教学设备及相关软件系统...</p>
                <div class="flex items-center text-xs text-gray-400">
                    <span>预算：¥600万</span>
                    <span class="mx-2">|</span>
                    <span>截止：2023-07-20</span>
                </div>
            </div>
        </div>
    </main>
    
    <!-- 底部导航 -->
    <footer class="bg-white border-t border-gray-200 py-2 px-6 flex justify-around items-center shadow-sm">
        <a href="list.html" class="flex flex-col items-center text-blue-600">
            <i class="ri-home-5-fill text-lg"></i>
            <span class="text-xs mt-1 font-medium">首页</span>
        </a>
        <a href="#" class="flex flex-col items-center text-gray-400">
            <i class="ri-file-list-line text-lg"></i>
            <span class="text-xs mt-1">分类</span>
        </a>
        <a href="#" class="flex flex-col items-center text-gray-400">
            <i class="ri-notification-line text-lg"></i>
            <span class="text-xs mt-1">消息</span>
        </a>
        <a href="vip.html" class="flex flex-col items-center text-gray-400">
            <i class="ri-vip-crown-line text-lg"></i>
            <span class="text-xs mt-1">会员</span>
        </a>
    </footer>
</body>
</html>