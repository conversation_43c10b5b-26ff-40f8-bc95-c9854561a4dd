<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            width: 375px;
            height: 812px;
            overflow: hidden;
        }
        .phone-container {
            width: 375px;
            height: 812px;
            overflow-y: auto;
            position: relative;
            background: white;
        }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .mini-capsule {
            position: absolute;
            top: 8px;
            right: 12px;
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .search-tag { transition: all 0.2s ease; }
        .search-tag:hover { transform: translateY(-1px); }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 小程序胶囊按钮 -->
        <div class="mini-capsule">
            <i class="fas fa-ellipsis-h text-gray-600 text-xs"></i>
        </div>
        
        <!-- 顶部搜索区域 -->
        <div class="gradient-bg px-4 pt-12 pb-6">
            <div class="flex items-center space-x-3 mb-4">
                <button class="text-white">
                    <i class="fas fa-arrow-left text-lg"></i>
                </button>
                <div class="flex-1 relative">
                    <input type="text" placeholder="搜索招标信息、公司名称..." 
                           class="w-full bg-white/90 backdrop-blur-sm rounded-full px-4 py-3 pl-10 pr-10 text-sm placeholder-gray-500 border-0 focus:outline-none focus:ring-2 focus:ring-white/50"
                           value="政府采购">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm"></i>
                    <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <button class="bg-white/20 backdrop-blur-sm rounded-full px-4 py-3 text-white text-sm">搜索</button>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="bg-white flex-1 overflow-y-auto">
            <!-- 搜索历史 -->
            <div class="px-4 py-4 border-b border-gray-100">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-sm font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-history text-gray-500 mr-2"></i>
                        搜索历史
                    </h3>
                    <button class="text-xs text-gray-500">清空</button>
                </div>
                <div class="flex flex-wrap gap-2">
                    <span class="search-tag bg-gray-100 text-gray-700 px-3 py-2 rounded-full text-xs cursor-pointer">政府采购</span>
                    <span class="search-tag bg-gray-100 text-gray-700 px-3 py-2 rounded-full text-xs cursor-pointer">工程建设</span>
                    <span class="search-tag bg-gray-100 text-gray-700 px-3 py-2 rounded-full text-xs cursor-pointer">医疗设备</span>
                    <span class="search-tag bg-gray-100 text-gray-700 px-3 py-2 rounded-full text-xs cursor-pointer">信息化系统</span>
                    <span class="search-tag bg-gray-100 text-gray-700 px-3 py-2 rounded-full text-xs cursor-pointer">装修改造</span>
                </div>
            </div>

            <!-- 热门搜索 -->
            <div class="px-4 py-4 border-b border-gray-100">
                <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-fire text-red-500 mr-2"></i>
                    热门搜索
                </h3>
                <div class="space-y-2">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <span class="bg-red-500 text-white text-xs px-2 py-1 rounded font-bold">1</span>
                            <span class="text-sm text-gray-800">智慧城市建设</span>
                        </div>
                        <span class="text-xs text-red-500">热</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <span class="bg-orange-500 text-white text-xs px-2 py-1 rounded font-bold">2</span>
                            <span class="text-sm text-gray-800">医院设备采购</span>
                        </div>
                        <span class="text-xs text-orange-500">新</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <span class="bg-yellow-500 text-white text-xs px-2 py-1 rounded font-bold">3</span>
                            <span class="text-sm text-gray-800">学校装修工程</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <span class="bg-gray-400 text-white text-xs px-2 py-1 rounded font-bold">4</span>
                            <span class="text-sm text-gray-800">道路建设项目</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <span class="bg-gray-400 text-white text-xs px-2 py-1 rounded font-bold">5</span>
                            <span class="text-sm text-gray-800">环保设备招标</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索结果 -->
            <div class="px-4 py-4">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-sm font-semibold text-gray-800">搜索结果</h3>
                    <span class="text-xs text-gray-500">找到 156 条相关信息</span>
                </div>
                
                <!-- 筛选条件 -->
                <div class="flex space-x-2 mb-4 overflow-x-auto pb-2">
                    <span class="bg-purple-100 text-purple-600 px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap">全部</span>
                    <span class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-xs whitespace-nowrap">最新发布</span>
                    <span class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-xs whitespace-nowrap">即将截止</span>
                    <span class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-xs whitespace-nowrap">金额最高</span>
                </div>

                <!-- 搜索结果列表 -->
                <div class="space-y-3">
                    <!-- 搜索结果项1 -->
                    <div class="bg-white rounded-xl p-4 border border-gray-100 shadow-sm">
                        <div class="flex items-start justify-between mb-2">
                            <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded-md text-xs font-medium">政府采购</span>
                            <span class="text-xs text-gray-400">3小时前</span>
                        </div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-2 line-clamp-2">
                            某市<span class="bg-yellow-200">政府采购</span>中心办公设备采购项目
                        </h4>
                        <div class="space-y-1 mb-3">
                            <div class="flex items-center text-xs text-gray-600">
                                <i class="fas fa-map-marker-alt w-4"></i>
                                <span>深圳市福田区</span>
                            </div>
                            <div class="flex items-center text-xs text-gray-600">
                                <i class="fas fa-coins w-4"></i>
                                <span>预算金额：200-300万元</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs">公开招标</span>
                            <button class="text-purple-600 text-xs font-medium">查看详情</button>
                        </div>
                    </div>

                    <!-- 搜索结果项2 -->
                    <div class="bg-white rounded-xl p-4 border border-gray-100 shadow-sm">
                        <div class="flex items-start justify-between mb-2">
                            <span class="bg-purple-100 text-purple-600 px-2 py-1 rounded-md text-xs font-medium">政府采购</span>
                            <span class="text-xs text-gray-400">6小时前</span>
                        </div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-2 line-clamp-2">
                            区<span class="bg-yellow-200">政府采购</span>网络安全设备采购公告
                        </h4>
                        <div class="space-y-1 mb-3">
                            <div class="flex items-center text-xs text-gray-600">
                                <i class="fas fa-map-marker-alt w-4"></i>
                                <span>杭州市西湖区</span>
                            </div>
                            <div class="flex items-center text-xs text-gray-600">
                                <i class="fas fa-coins w-4"></i>
                                <span>预算金额：150-250万元</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs">公开招标</span>
                            <button class="text-purple-600 text-xs font-medium">查看详情</button>
                        </div>
                    </div>

                    <!-- 搜索结果项3 -->
                    <div class="bg-white rounded-xl p-4 border border-gray-100 shadow-sm">
                        <div class="flex items-start justify-between mb-2">
                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded-md text-xs font-medium">政府采购</span>
                            <span class="text-xs text-gray-400">1天前</span>
                        </div>
                        <h4 class="text-sm font-semibold text-gray-800 mb-2 line-clamp-2">
                            市<span class="bg-yellow-200">政府采购</span>中心车辆采购项目招标
                        </h4>
                        <div class="space-y-1 mb-3">
                            <div class="flex items-center text-xs text-gray-600">
                                <i class="fas fa-map-marker-alt w-4"></i>
                                <span>成都市锦江区</span>
                            </div>
                            <div class="flex items-center text-xs text-gray-600">
                                <i class="fas fa-coins w-4"></i>
                                <span>预算金额：400-600万元</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs">公开招标</span>
                            <button class="text-purple-600 text-xs font-medium">查看详情</button>
                        </div>
                    </div>
                </div>

                <!-- 加载更多 -->
                <div class="text-center py-6">
                    <button class="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-2 rounded-full text-sm font-medium">
                        查看更多结果
                    </button>
                </div>
            </div>
        </div>


    </div>
</body>
</html>
