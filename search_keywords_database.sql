-- =============================================
-- 热门搜索关键词数据库设计
-- 创建时间: 2025-07-15
-- 描述: 支持热门搜索功能的完整数据库结构
-- =============================================

-- 使用招标系统数据库
USE bidding_system;

-- =============================================
-- 1. 搜索关键词主表
-- =============================================
CREATE TABLE search_keywords (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关键词ID',
    keyword VARCHAR(100) NOT NULL COMMENT '搜索关键词',
    search_count INT DEFAULT 0 COMMENT '搜索次数',
    trend ENUM('up', 'down', 'stable') DEFAULT 'stable' COMMENT '趋势(上升/下降/稳定)',
    is_hot BOOLEAN DEFAULT FALSE COMMENT '是否热门',
    is_new BOOLEAN DEFAULT FALSE COMMENT '是否新词',
    category VARCHAR(50) COMMENT '关键词分类',
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active' COMMENT '状态',
    
    -- 统计字段
    daily_search_count INT DEFAULT 0 COMMENT '日搜索次数',
    weekly_search_count INT DEFAULT 0 COMMENT '周搜索次数',
    monthly_search_count INT DEFAULT 0 COMMENT '月搜索次数',
    last_search_time TIMESTAMP NULL COMMENT '最后搜索时间',
    
    -- 趋势计算字段
    yesterday_count INT DEFAULT 0 COMMENT '昨日搜索次数',
    last_week_count INT DEFAULT 0 COMMENT '上周搜索次数',
    trend_score DECIMAL(5,2) DEFAULT 0 COMMENT '趋势分数',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_keyword (keyword),
    INDEX idx_search_count (search_count DESC),
    INDEX idx_is_hot (is_hot),
    INDEX idx_is_new (is_new),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_trend (trend),
    INDEX idx_last_search_time (last_search_time)
) COMMENT '搜索关键词主表';

-- =============================================
-- 2. 搜索记录表
-- =============================================
CREATE TABLE search_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    keyword_id BIGINT COMMENT '关键词ID',
    keyword VARCHAR(100) NOT NULL COMMENT '搜索关键词',
    user_id BIGINT COMMENT '用户ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    search_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '搜索时间',
    result_count INT DEFAULT 0 COMMENT '搜索结果数量',
    click_count INT DEFAULT 0 COMMENT '点击次数',
    
    FOREIGN KEY (keyword_id) REFERENCES search_keywords(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_keyword_id (keyword_id),
    INDEX idx_user_id (user_id),
    INDEX idx_search_time (search_time),
    INDEX idx_keyword (keyword)
) COMMENT '搜索记录表';

-- =============================================
-- 3. 关键词分类表
-- =============================================
CREATE TABLE keyword_categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    category_name VARCHAR(50) NOT NULL UNIQUE COMMENT '分类名称',
    category_desc VARCHAR(200) COMMENT '分类描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_sort_order (sort_order)
) COMMENT '关键词分类表';

-- =============================================
-- 4. 热门搜索配置表
-- =============================================
CREATE TABLE hot_search_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_name VARCHAR(100) NOT NULL COMMENT '配置名称',
    min_search_count INT DEFAULT 10 COMMENT '最小搜索次数',
    hot_threshold INT DEFAULT 100 COMMENT '热门阈值',
    new_threshold_days INT DEFAULT 7 COMMENT '新词天数阈值',
    trend_calculation_days INT DEFAULT 7 COMMENT '趋势计算天数',
    max_hot_keywords INT DEFAULT 20 COMMENT '最大热门关键词数',
    max_new_keywords INT DEFAULT 10 COMMENT '最大新词数',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '热门搜索配置表';

-- =============================================
-- 插入初始数据
-- =============================================

-- 插入关键词分类
INSERT INTO keyword_categories (category_name, category_desc, sort_order) VALUES
('建筑工程', '建筑、装修、基建类项目', 1),
('信息技术', 'IT、软件、系统集成类项目', 2),
('医疗设备', '医疗器械、设备采购类项目', 3),
('教育装备', '教育设备、器材采购类项目', 4),
('环保工程', '环保、节能、绿色项目', 5),
('交通运输', '道路、桥梁、交通设施类项目', 6),
('能源电力', '电力、新能源、能源设施类项目', 7),
('其他', '其他类型项目', 99);

-- 插入热门搜索配置
INSERT INTO hot_search_config (
    config_name, min_search_count, hot_threshold, new_threshold_days,
    trend_calculation_days, max_hot_keywords, max_new_keywords
) VALUES (
    'default_config', 10, 100, 7, 7, 20, 10
);

-- 插入示例关键词数据
INSERT INTO search_keywords (
    keyword, search_count, trend, is_hot, is_new, category,
    daily_search_count, weekly_search_count, monthly_search_count,
    yesterday_count, last_week_count, trend_score, last_search_time
) VALUES
('智慧城市建设', 1250, 'up', TRUE, FALSE, '建筑工程', 45, 280, 1250, 38, 220, 15.5, NOW()),
('医院建设', 980, 'up', TRUE, FALSE, '建筑工程', 35, 210, 980, 28, 180, 12.3, NOW()),
('5G基站', 856, 'stable', TRUE, FALSE, '信息技术', 28, 185, 856, 30, 185, 0.0, NOW()),
('教育装备', 742, 'down', TRUE, FALSE, '教育装备', 22, 145, 742, 28, 165, -8.2, NOW()),
('环保工程', 698, 'up', TRUE, FALSE, '环保工程', 25, 160, 698, 20, 140, 8.7, NOW()),
('数据中心', 634, 'up', TRUE, FALSE, '信息技术', 24, 148, 634, 18, 125, 11.2, NOW()),
('新能源', 587, 'stable', TRUE, FALSE, '能源电力', 20, 125, 587, 21, 125, 0.0, NOW()),
('公路建设', 523, 'down', TRUE, FALSE, '交通运输', 18, 110, 523, 22, 130, -6.8, NOW()),
('政府采购', 456, 'stable', FALSE, FALSE, '其他', 15, 95, 456, 16, 95, 0.0, NOW()),
('信息系统', 398, 'up', FALSE, FALSE, '信息技术', 14, 88, 398, 10, 70, 14.3, NOW()),
('人工智能', 234, 'up', FALSE, TRUE, '信息技术', 12, 65, 234, 8, 45, 22.2, NOW()),
('区块链', 187, 'up', FALSE, TRUE, '信息技术', 9, 48, 187, 6, 32, 25.0, NOW());

-- =============================================
-- 创建视图
-- =============================================

-- 热门搜索关键词视图
CREATE VIEW v_hot_keywords AS
SELECT 
    sk.id,
    sk.keyword,
    sk.search_count,
    sk.trend,
    sk.is_hot,
    sk.is_new,
    sk.category,
    sk.daily_search_count,
    sk.weekly_search_count,
    sk.monthly_search_count,
    sk.trend_score,
    sk.last_search_time,
    kc.category_desc
FROM search_keywords sk
LEFT JOIN keyword_categories kc ON sk.category = kc.category_name
WHERE sk.status = 'active'
ORDER BY sk.search_count DESC;

-- 搜索统计视图
CREATE VIEW v_search_statistics AS
SELECT 
    DATE(sr.search_time) as search_date,
    sr.keyword,
    COUNT(*) as search_count,
    COUNT(DISTINCT sr.user_id) as unique_users,
    COUNT(DISTINCT sr.ip_address) as unique_ips,
    AVG(sr.result_count) as avg_result_count
FROM search_records sr
WHERE sr.search_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(sr.search_time), sr.keyword
ORDER BY search_date DESC, search_count DESC;

-- =============================================
-- 创建存储过程
-- =============================================

-- 记录搜索行为
DELIMITER //
CREATE PROCEDURE RecordSearch(
    IN p_keyword VARCHAR(100),
    IN p_user_id BIGINT,
    IN p_ip_address VARCHAR(45),
    IN p_user_agent TEXT,
    IN p_result_count INT
)
BEGIN
    DECLARE v_keyword_id BIGINT DEFAULT NULL;
    DECLARE v_category VARCHAR(50) DEFAULT '其他';
    
    -- 开始事务
    START TRANSACTION;
    
    -- 查找或创建关键词
    SELECT id, category INTO v_keyword_id, v_category 
    FROM search_keywords 
    WHERE keyword = p_keyword;
    
    IF v_keyword_id IS NULL THEN
        -- 自动分类关键词
        SET v_category = AutoCategorizeKeyword(p_keyword);
        
        -- 插入新关键词
        INSERT INTO search_keywords (keyword, category, search_count, daily_search_count, last_search_time)
        VALUES (p_keyword, v_category, 1, 1, NOW());
        
        SET v_keyword_id = LAST_INSERT_ID();
    ELSE
        -- 更新搜索统计
        UPDATE search_keywords 
        SET search_count = search_count + 1,
            daily_search_count = daily_search_count + 1,
            last_search_time = NOW()
        WHERE id = v_keyword_id;
    END IF;
    
    -- 插入搜索记录
    INSERT INTO search_records (keyword_id, keyword, user_id, ip_address, user_agent, result_count)
    VALUES (v_keyword_id, p_keyword, p_user_id, p_ip_address, p_user_agent, p_result_count);
    
    COMMIT;
END //
DELIMITER ;

-- 自动分类关键词函数
DELIMITER //
CREATE FUNCTION AutoCategorizeKeyword(p_keyword VARCHAR(100))
RETURNS VARCHAR(50)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_category VARCHAR(50) DEFAULT '其他';
    
    -- 简单的关键词分类逻辑
    IF p_keyword LIKE '%建设%' OR p_keyword LIKE '%装修%' OR p_keyword LIKE '%工程%' THEN
        SET v_category = '建筑工程';
    ELSEIF p_keyword LIKE '%系统%' OR p_keyword LIKE '%软件%' OR p_keyword LIKE '%信息化%' OR p_keyword LIKE '%智能%' THEN
        SET v_category = '信息技术';
    ELSEIF p_keyword LIKE '%医疗%' OR p_keyword LIKE '%医院%' OR p_keyword LIKE '%设备%' THEN
        SET v_category = '医疗设备';
    ELSEIF p_keyword LIKE '%教育%' OR p_keyword LIKE '%学校%' OR p_keyword LIKE '%装备%' THEN
        SET v_category = '教育装备';
    ELSEIF p_keyword LIKE '%环保%' OR p_keyword LIKE '%节能%' OR p_keyword LIKE '%绿色%' THEN
        SET v_category = '环保工程';
    ELSEIF p_keyword LIKE '%道路%' OR p_keyword LIKE '%交通%' OR p_keyword LIKE '%桥梁%' THEN
        SET v_category = '交通运输';
    ELSEIF p_keyword LIKE '%电力%' OR p_keyword LIKE '%能源%' OR p_keyword LIKE '%发电%' THEN
        SET v_category = '能源电力';
    END IF;
    
    RETURN v_category;
END //
DELIMITER ;

-- 更新热门关键词状态
DELIMITER //
CREATE PROCEDURE UpdateHotKeywords()
BEGIN
    DECLARE v_hot_threshold INT DEFAULT 100;
    DECLARE v_max_hot_keywords INT DEFAULT 20;

    -- 获取配置
    SELECT hot_threshold, max_hot_keywords
    INTO v_hot_threshold, v_max_hot_keywords
    FROM hot_search_config
    WHERE config_name = 'default_config';

    -- 重置所有关键词的热门状态
    UPDATE search_keywords SET is_hot = FALSE;

    -- 设置热门关键词
    UPDATE search_keywords
    SET is_hot = TRUE
    WHERE id IN (
        SELECT id FROM (
            SELECT id FROM search_keywords
            WHERE search_count >= v_hot_threshold
            AND status = 'active'
            ORDER BY search_count DESC
            LIMIT v_max_hot_keywords
        ) tmp
    );
END //
DELIMITER ;

-- 更新新词状态
DELIMITER //
CREATE PROCEDURE UpdateNewKeywords()
BEGIN
    DECLARE v_new_threshold_days INT DEFAULT 7;
    DECLARE v_max_new_keywords INT DEFAULT 10;

    -- 获取配置
    SELECT new_threshold_days, max_new_keywords
    INTO v_new_threshold_days, v_max_new_keywords
    FROM hot_search_config
    WHERE config_name = 'default_config';

    -- 重置所有关键词的新词状态
    UPDATE search_keywords SET is_new = FALSE;

    -- 设置新词
    UPDATE search_keywords
    SET is_new = TRUE
    WHERE id IN (
        SELECT id FROM (
            SELECT id FROM search_keywords
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL v_new_threshold_days DAY)
            AND status = 'active'
            ORDER BY search_count DESC
            LIMIT v_max_new_keywords
        ) tmp
    );
END //
DELIMITER ;

-- 计算趋势
DELIMITER //
CREATE PROCEDURE CalculateTrend()
BEGIN
    -- 计算趋势分数并更新趋势状态
    UPDATE search_keywords
    SET trend_score = CASE
        WHEN yesterday_count > 0 THEN
            ((daily_search_count - yesterday_count) / yesterday_count) * 100
        ELSE 0
    END,
    trend = CASE
        WHEN yesterday_count > 0 AND ((daily_search_count - yesterday_count) / yesterday_count) > 0.1 THEN 'up'
        WHEN yesterday_count > 0 AND ((daily_search_count - yesterday_count) / yesterday_count) < -0.1 THEN 'down'
        ELSE 'stable'
    END
    WHERE status = 'active';
END //
DELIMITER ;

-- 每日统计重置
DELIMITER //
CREATE PROCEDURE DailyReset()
BEGIN
    -- 保存昨日数据
    UPDATE search_keywords
    SET yesterday_count = daily_search_count,
        last_week_count = CASE
            WHEN DAYOFWEEK(NOW()) = 2 THEN weekly_search_count  -- 周一重置
            ELSE last_week_count
        END;

    -- 重置日统计
    UPDATE search_keywords SET daily_search_count = 0;

    -- 重置周统计（每周一）
    IF DAYOFWEEK(NOW()) = 2 THEN
        UPDATE search_keywords SET weekly_search_count = 0;
    END IF;

    -- 重置月统计（每月1号）
    IF DAY(NOW()) = 1 THEN
        UPDATE search_keywords SET monthly_search_count = 0;
    END IF;

    -- 更新热门关键词
    CALL UpdateHotKeywords();

    -- 更新新词
    CALL UpdateNewKeywords();

    -- 计算趋势
    CALL CalculateTrend();
END //
DELIMITER ;

-- 获取热门搜索API数据
DELIMITER //
CREATE PROCEDURE GetHotSearchKeywords(IN p_limit INT)
BEGIN
    SELECT
        keyword,
        search_count,
        trend,
        is_hot,
        is_new
    FROM search_keywords
    WHERE status = 'active'
    AND (is_hot = TRUE OR is_new = TRUE)
    ORDER BY
        is_hot DESC,
        search_count DESC,
        is_new DESC
    LIMIT p_limit;
END //
DELIMITER ;

-- =============================================
-- 创建定时任务事件
-- =============================================

-- 开启事件调度器
SET GLOBAL event_scheduler = ON;

-- 每日凌晨执行统计重置
CREATE EVENT IF NOT EXISTS daily_keyword_reset
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURDATE() + INTERVAL 1 DAY, '00:30:00')
DO
  CALL DailyReset();

-- 每小时更新热门关键词
CREATE EVENT IF NOT EXISTS hourly_hot_update
ON SCHEDULE EVERY 1 HOUR
DO
  CALL UpdateHotKeywords();

-- =============================================
-- 创建触发器
-- =============================================

-- 搜索记录插入时自动更新统计
DELIMITER //
CREATE TRIGGER tr_search_record_after_insert
AFTER INSERT ON search_records
FOR EACH ROW
BEGIN
    -- 更新关键词的搜索统计
    UPDATE search_keywords
    SET search_count = search_count + 1,
        daily_search_count = daily_search_count + 1,
        weekly_search_count = weekly_search_count + 1,
        monthly_search_count = monthly_search_count + 1,
        last_search_time = NEW.search_time
    WHERE id = NEW.keyword_id;
END //
DELIMITER ;

-- =============================================
-- 数据库操作详细步骤说明
-- =============================================

/*
## 数据库操作流程详解

### 1. 用户搜索时的操作流程

#### 步骤1：记录搜索行为
```sql
CALL RecordSearch('智慧城市建设', 1001, '192.168.1.100', 'Mozilla/5.0...', 25);
```

#### 步骤2：自动处理流程
- 检查关键词是否存在
- 不存在则自动创建并分类
- 更新搜索统计数据
- 插入搜索记录

### 2. 获取热门搜索数据

#### API接口调用
```sql
CALL GetHotSearchKeywords(20);
```

#### 返回数据格式
```json
[
    {
        "keyword": "智慧城市建设",
        "search_count": 1250,
        "trend": "up",
        "is_hot": true,
        "is_new": false
    }
]
```

### 3. 定时维护任务

#### 每日凌晨00:30自动执行
- 保存昨日搜索数据
- 重置日/周/月统计
- 更新热门关键词状态
- 更新新词状态
- 计算趋势分数

#### 每小时更新热门关键词
- 根据最新搜索量更新热门状态

### 4. 手动维护操作

#### 添加新的关键词分类
```sql
INSERT INTO keyword_categories (category_name, category_desc, sort_order)
VALUES ('新分类', '分类描述', 10);
```

#### 手动设置热门关键词
```sql
UPDATE search_keywords SET is_hot = TRUE WHERE keyword = '特定关键词';
```

#### 禁用某个关键词
```sql
UPDATE search_keywords SET status = 'banned' WHERE keyword = '违规关键词';
```

### 5. 数据清理

#### 清理过期搜索记录（保留3个月）
```sql
DELETE FROM search_records
WHERE search_time < DATE_SUB(NOW(), INTERVAL 3 MONTH);
```

#### 清理无效关键词
```sql
DELETE FROM search_keywords
WHERE search_count = 0
AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 6. 性能优化建议

1. 定期分析表性能
2. 对大表进行分区
3. 使用Redis缓存热门搜索结果
4. 监控慢查询并优化索引
5. 定期清理历史数据

*/
