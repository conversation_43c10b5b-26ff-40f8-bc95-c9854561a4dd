<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浏览记录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            width: 375px;
            height: 812px;
            overflow: hidden;
        }
        .phone-container {
            width: 375px;
            height: 812px;
            overflow-y: auto;
            position: relative;
            background: white;
        }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .mini-capsule {
            position: absolute;
            top: 8px;
            right: 12px;
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .swipe-item { transition: transform 0.3s ease; }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 小程序胶囊按钮 -->
        <div class="mini-capsule">
            <i class="fas fa-ellipsis-h text-gray-600 text-xs"></i>
        </div>
        
        <!-- 顶部导航 -->
        <div class="gradient-bg px-4 pt-12 pb-4">
            <div class="flex items-center space-x-3">
                <button class="text-white">
                    <i class="fas fa-arrow-left text-lg"></i>
                </button>
                <h1 class="text-white text-lg font-semibold flex-1">我的浏览</h1>
                <button class="text-white text-sm">编辑</button>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="bg-white flex-1 overflow-y-auto">
            <!-- 统计信息 -->
            <div class="px-4 py-4 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="text-center">
                            <div class="text-lg font-bold text-purple-600">24</div>
                            <div class="text-xs text-gray-600">今日浏览</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-blue-600">156</div>
                            <div class="text-xs text-gray-600">总浏览量</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-green-600">8</div>
                            <div class="text-xs text-gray-600">已收藏</div>
                        </div>
                    </div>
                    <button class="bg-white/80 backdrop-blur-sm rounded-lg px-3 py-2 text-xs text-gray-600">
                        <i class="fas fa-trash mr-1"></i>清空记录
                    </button>
                </div>
            </div>

            <!-- 时间分组 -->
            <div class="px-4 py-2">
                <!-- 今天 -->
                <div class="mb-4">
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <h3 class="text-sm font-semibold text-gray-800">今天</h3>
                        <span class="text-xs text-gray-500">6条记录</span>
                    </div>
                    <div class="space-y-3 mt-3">
                        <!-- 浏览记录项1 -->
                        <div class="swipe-item bg-white rounded-xl p-4 card-shadow border border-gray-100">
                            <div class="flex items-start space-x-3">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between mb-1">
                                        <h4 class="text-sm font-semibold text-gray-800 line-clamp-2">某市政府办公大楼装修改造工程招标公告</h4>
                                        <span class="text-xs text-gray-400 ml-2">14:30</span>
                                    </div>
                                    <div class="flex items-center space-x-2 mb-2">
                                        <span class="bg-red-100 text-red-600 px-2 py-1 rounded text-xs">紧急</span>
                                        <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded text-xs">工程建设</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-600">预算：500-800万元</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 浏览记录项2 -->
                        <div class="swipe-item bg-white rounded-xl p-4 card-shadow border border-gray-100">
                            <div class="flex items-start space-x-3">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between mb-1">
                                        <h4 class="text-sm font-semibold text-gray-800 line-clamp-2">智慧城市信息化系统建设项目采购公告</h4>
                                        <span class="text-xs text-gray-400 ml-2">12:15</span>
                                    </div>
                                    <div class="flex items-center space-x-2 mb-2">
                                        <span class="bg-orange-100 text-orange-600 px-2 py-1 rounded text-xs">热门</span>
                                        <span class="bg-purple-100 text-purple-600 px-2 py-1 rounded text-xs">信息化</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-600">预算：1000-1500万元</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 浏览记录项3 -->
                        <div class="swipe-item bg-white rounded-xl p-4 card-shadow border border-gray-100">
                            <div class="flex items-start space-x-3">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between mb-1">
                                        <h4 class="text-sm font-semibold text-gray-800 line-clamp-2">医疗设备采购项目招标公告</h4>
                                        <span class="text-xs text-gray-400 ml-2">09:45</span>
                                    </div>
                                    <div class="flex items-center space-x-2 mb-2">
                                        <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded text-xs">新发布</span>
                                        <span class="bg-pink-100 text-pink-600 px-2 py-1 rounded text-xs">医疗设备</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-600">预算：300-500万元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 昨天 -->
                <div class="mb-4">
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <h3 class="text-sm font-semibold text-gray-800">昨天</h3>
                        <span class="text-xs text-gray-500">8条记录</span>
                    </div>
                    <div class="space-y-3 mt-3">
                        <!-- 浏览记录项4 -->
                        <div class="swipe-item bg-white rounded-xl p-4 card-shadow border border-gray-100">
                            <div class="flex items-start space-x-3">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between mb-1">
                                        <h4 class="text-sm font-semibold text-gray-800 line-clamp-2">城市道路改造工程施工招标</h4>
                                        <span class="text-xs text-gray-400 ml-2">16:20</span>
                                    </div>
                                    <div class="flex items-center space-x-2 mb-2">
                                        <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs">进行中</span>
                                        <span class="bg-yellow-100 text-yellow-600 px-2 py-1 rounded text-xs">道路工程</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-600">预算：2000-3000万元</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 浏览记录项5 -->
                        <div class="swipe-item bg-white rounded-xl p-4 card-shadow border border-gray-100">
                            <div class="flex items-start space-x-3">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between mb-1">
                                        <h4 class="text-sm font-semibold text-gray-800 line-clamp-2">环保设备采购及安装项目</h4>
                                        <span class="text-xs text-gray-400 ml-2">14:10</span>
                                    </div>
                                    <div class="flex items-center space-x-2 mb-2">
                                        <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs">环保</span>
                                        <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded text-xs">设备采购</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-gray-600">预算：800-1200万元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 更早 -->
                <div class="mb-4">
                    <div class="flex items-center justify-between py-2 border-b border-gray-100">
                        <h3 class="text-sm font-semibold text-gray-800">更早</h3>
                        <span class="text-xs text-gray-500">142条记录</span>
                    </div>
                    <div class="text-center py-6">
                        <button class="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-2 rounded-full text-sm font-medium">
                            查看更多历史记录
                        </button>
                    </div>
                </div>
            </div>
        </div>


    </div>
</body>
</html>
