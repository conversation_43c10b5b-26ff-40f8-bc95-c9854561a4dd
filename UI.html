<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招标信息网站UI设计展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            padding: 30px;
            justify-content: center;
            align-items: flex-start;
        }
        .page-frame {
            background: white;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 8px solid #1f2937;
        }
        .page-frame:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }
        .page-title {
            text-align: center;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
            color: #4a5568;
        }
        iframe {
            border: none;
            border-radius: 15px;
            width: 375px;
            height: 812px;
            display: block;
            background: white;
        }
        .header {
            text-align: center;
            padding: 40px 20px;
            color: white;
        }
        .header h1 {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        .features {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            color: white;
            min-width: 150px;
        }
        .feature i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }
        .feature h3 {
            font-size: 1rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .feature p {
            font-size: 0.8rem;
            opacity: 0.8;
        }
        .tech-stack {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin: 20px auto;
            max-width: 800px;
            color: white;
            text-align: center;
        }
        .tech-stack h3 {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .tech-tags {
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
        }
        .tech-tag {
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <div class="header">
        <h1><i class="fas fa-gavel mr-3"></i>招标信息网站UI设计</h1>
        <p>现代极简主义美学与功能的完美平衡 | 微信公众号招标信息平台</p>
        
        <!-- 核心功能特性 -->
        <div class="features">
            <div class="feature">
                <i class="fas fa-list"></i>
                <h3>信息列表</h3>
                <p>智能筛选浏览</p>
            </div>
            <div class="feature">
                <i class="fas fa-file-alt"></i>
                <h3>详情展示</h3>
                <p>完整信息呈现</p>
            </div>
            <div class="feature">
                <i class="fas fa-search"></i>
                <h3>智能搜索</h3>
                <p>精准快速查找</p>
            </div>
            <div class="feature">
                <i class="fas fa-history"></i>
                <h3>浏览记录</h3>
                <p>个人历史管理</p>
            </div>
            <div class="feature">
                <i class="fas fa-crown"></i>
                <h3>会员服务</h3>
                <p>高级功能解锁</p>
            </div>
            <div class="feature">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>错误处理</h3>
                <p>友好错误提示</p>
            </div>
        </div>

        <!-- 技术栈展示 -->
        <div class="tech-stack">
            <h3><i class="fas fa-code mr-2"></i>技术实现</h3>
            <div class="tech-tags">
                <span class="tech-tag">HTML5</span>
                <span class="tech-tag">TailwindCSS</span>
                <span class="tech-tag">Font Awesome</span>
                <span class="tech-tag">响应式设计</span>
                <span class="tech-tag">移动端优化</span>
                <span class="tech-tag">现代浏览器</span>
            </div>
        </div>
    </div>

    <!-- 页面展示容器 -->
    <div class="container">
        <!-- 列表页面 -->
        <div class="page-frame">
            <div class="page-title">
                <i class="fas fa-list text-purple-600 mr-2"></i>
                招标信息列表页面
            </div>
            <iframe src="list.html" title="招标信息列表页面"></iframe>
        </div>

        <!-- 详情页面 -->
        <div class="page-frame">
            <div class="page-title">
                <i class="fas fa-file-alt text-blue-600 mr-2"></i>
                招标详情页面
            </div>
            <iframe src="detail.html" title="招标详情页面"></iframe>
        </div>

        <!-- 搜索页面 -->
        <div class="page-frame">
            <div class="page-title">
                <i class="fas fa-search text-green-600 mr-2"></i>
                搜索功能页面
            </div>
            <iframe src="search.html" title="搜索功能页面"></iframe>
        </div>

        <!-- 浏览记录页面 -->
        <div class="page-frame">
            <div class="page-title">
                <i class="fas fa-history text-orange-600 mr-2"></i>
                浏览记录页面
            </div>
            <iframe src="history.html" title="浏览记录页面"></iframe>
        </div>

        <!-- 开通会员页面 -->
        <div class="page-frame">
            <div class="page-title">
                <i class="fas fa-crown text-yellow-600 mr-2"></i>
                开通会员页面
            </div>
            <iframe src="member.html" title="开通会员页面"></iframe>
        </div>

        <!-- 错误显示页面 -->
        <div class="page-frame">
            <div class="page-title">
                <i class="fas fa-exclamation-triangle text-red-600 mr-2"></i>
                错误显示页面
            </div>
            <iframe src="error.html" title="错误显示页面"></iframe>
        </div>
    </div>

    <!-- 页面底部信息 -->
    <div class="text-center text-white py-8">
        <p class="text-sm opacity-80">
            <i class="fas fa-palette mr-2"></i>
            设计风格：现代极简主义 | 清新渐变配色 | 卡片化布局 | 微交互设计
        </p>
        <p class="text-sm opacity-80 mt-2">
            <i class="fas fa-mobile-alt mr-2"></i>
            尺寸规格：375×812px | 移动端优化 | 响应式设计
        </p>
    </div>
</body>
</html>
