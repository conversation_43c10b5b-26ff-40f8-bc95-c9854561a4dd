<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浏览记录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            width: 375px;
            height: 812px;
            overflow: hidden;
        }
        .phone-container {
            width: 375px;
            height: 812px;
            overflow-y: auto;
            position: relative;
            background: white;
        }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .mini-capsule {
            position: absolute;
            top: 8px;
            right: 12px;
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .swipe-item { 
            transition: transform 0.3s ease, opacity 0.3s ease; 
            cursor: pointer;
        }
        .swipe-item:hover { transform: translateY(-2px); }
        .swipe-item.removing { 
            transform: translateX(-100%); 
            opacity: 0; 
        }
        .edit-mode .swipe-item {
            transform: translateX(40px);
        }
        .delete-btn {
            position: absolute;
            left: 8px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            background: #ef4444;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .edit-mode .delete-btn {
            opacity: 1;
        }
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 小程序胶囊按钮 -->
        <div class="mini-capsule">
            <i class="fas fa-ellipsis-h text-gray-600 text-xs"></i>
        </div>
        
        <!-- 顶部导航 -->
        <div class="gradient-bg px-4 pt-12 pb-4">
            <div class="flex items-center space-x-3">
                <button class="text-white" onclick="goBack()">
                    <i class="fas fa-arrow-left text-lg"></i>
                </button>
                <h1 class="text-white text-lg font-semibold flex-1">浏览记录</h1>
                <button class="text-white text-sm" id="editBtn" onclick="toggleEditMode()">编辑</button>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="bg-white flex-1 overflow-y-auto">
            <!-- 统计信息 -->
            <div class="px-4 py-4 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="text-center">
                            <div class="text-lg font-bold text-purple-600" id="todayCount">0</div>
                            <div class="text-xs text-gray-600">今日浏览</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-blue-600" id="totalCount">0</div>
                            <div class="text-xs text-gray-600">总浏览量</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-green-600" id="favoriteCount">0</div>
                            <div class="text-xs text-gray-600">已收藏</div>
                        </div>
                    </div>
                    <button class="bg-white/80 backdrop-blur-sm rounded-lg px-3 py-2 text-xs text-gray-600" onclick="clearAllHistory()">
                        <i class="fas fa-trash mr-1"></i>清空记录
                    </button>
                </div>
            </div>

            <!-- 搜索框 -->
            <div class="px-4 py-3 border-b border-gray-100">
                <div class="relative">
                    <input type="text" id="searchInput" placeholder="搜索浏览记录..." 
                           class="w-full bg-gray-50 rounded-lg pl-10 pr-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:bg-white">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm"></i>
                </div>
            </div>

            <!-- 时间分组 -->
            <div class="px-4 py-2" id="historyContainer">
                <!-- 加载状态 -->
                <div id="loadingState" class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-2"></i>
                    <p class="text-gray-500 text-sm">加载中...</p>
                </div>
                
                <!-- 空状态 -->
                <div id="emptyState" class="text-center py-12" style="display: none;">
                    <i class="fas fa-history text-gray-300 text-4xl mb-4"></i>
                    <p class="text-gray-500 text-sm mb-2">暂无浏览记录</p>
                    <p class="text-gray-400 text-xs">您还没有浏览过任何招标信息</p>
                </div>

                <!-- 搜索无结果 -->
                <div id="noResultState" class="text-center py-12" style="display: none;">
                    <i class="fas fa-search text-gray-300 text-4xl mb-4"></i>
                    <p class="text-gray-500 text-sm mb-2">未找到相关记录</p>
                    <p class="text-gray-400 text-xs">尝试使用其他关键词搜索</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟API返回的浏览记录数据
        const mockApiResponse = {
            "code": 0,
            "data": {
                "list": [
                    {
                        "id": 1,
                        "article_id": 123,
                        "article_title": "中国银行周口分行办公楼15层大会议室装修改造工程",
                        "category_name": "工程建设",
                        "city_name": "周口市",
                        "browse_time": "2025-07-25T14:30:00Z"
                    },
                    {
                        "id": 2,
                        "article_id": 124,
                        "article_title": "智慧城市信息化系统建设项目采购公告",
                        "category_name": "信息化",
                        "city_name": "上海市",
                        "browse_time": "2025-07-25T12:15:00Z"
                    },
                    {
                        "id": 3,
                        "article_id": 125,
                        "article_title": "医疗设备采购项目招标公告",
                        "category_name": "医疗设备",
                        "city_name": "广州市",
                        "browse_time": "2025-07-25T09:45:00Z"
                    },
                    {
                        "id": 4,
                        "article_id": 126,
                        "article_title": "城市道路改造工程施工招标",
                        "category_name": "道路工程",
                        "city_name": "北京市",
                        "browse_time": "2025-07-24T16:20:00Z"
                    },
                    {
                        "id": 5,
                        "article_id": 127,
                        "article_title": "环保设备采购及安装项目",
                        "category_name": "环保设备",
                        "city_name": "深圳市",
                        "browse_time": "2025-07-24T14:10:00Z"
                    },
                    {
                        "id": 6,
                        "article_id": 128,
                        "article_title": "学校教学楼建设工程招标",
                        "category_name": "建筑工程",
                        "city_name": "杭州市",
                        "browse_time": "2025-07-23T10:30:00Z"
                    }
                ],
                "total": 6
            }
        };

        // 全局变量
        let browseHistoryData = [];
        let filteredHistory = [];

        // 全局变量
        let isEditMode = false;

        // 格式化时间（处理ISO 8601格式）
        function formatTime(timeStr) {
            const now = new Date();
            const time = new Date(timeStr); // 自动处理ISO 8601格式
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
            const itemDate = new Date(time.getFullYear(), time.getMonth(), time.getDate());

            if (itemDate.getTime() === today.getTime()) {
                return {
                    group: '今天',
                    time: time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
                };
            } else if (itemDate.getTime() === yesterday.getTime()) {
                return {
                    group: '昨天',
                    time: time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
                };
            } else {
                const diffDays = Math.floor((today - itemDate) / (24 * 60 * 60 * 1000));
                if (diffDays <= 7) {
                    return {
                        group: '本周',
                        time: time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
                    };
                } else {
                    return {
                        group: '更早',
                        time: time.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
                    };
                }
            }
        }

        // 获取分类标签颜色
        function getCategoryColor(category) {
            const colors = {
                '工程建设': 'bg-blue-100 text-blue-600',
                '信息化': 'bg-purple-100 text-purple-600',
                '医疗设备': 'bg-pink-100 text-pink-600',
                '道路工程': 'bg-yellow-100 text-yellow-600',
                '环保设备': 'bg-green-100 text-green-600',
                '建筑工程': 'bg-gray-100 text-gray-600',
                '设备采购': 'bg-indigo-100 text-indigo-600',
                '市政工程': 'bg-orange-100 text-orange-600',
                '装修装饰': 'bg-cyan-100 text-cyan-600',
                '电力工程': 'bg-red-100 text-red-600'
            };
            return colors[category] || 'bg-gray-100 text-gray-600';
        }

        // 渲染浏览记录项
        function renderHistoryItem(item) {
            const timeInfo = formatTime(item.browse_time);

            return `
                <div class="swipe-item bg-white rounded-xl p-4 card-shadow border border-gray-100 relative" data-id="${item.id}" data-article-id="${item.article_id}">
                    <div class="delete-btn" onclick="deleteHistoryItem(${item.id})">
                        <i class="fas fa-minus text-white text-xs"></i>
                    </div>
                    <div class="flex items-start space-x-3" onclick="viewDetail(${item.article_id})">
                        <div class="flex-1 min-w-0">
                            <div class="flex items-start justify-between mb-2">
                                <h4 class="text-sm font-semibold text-gray-800 line-clamp-2 flex-1">${item.article_title}</h4>
                                <span class="text-xs text-gray-400 ml-2 whitespace-nowrap">${timeInfo.time}</span>
                            </div>
                            <div class="flex items-center space-x-2 mb-2">
                                <span class="${getCategoryColor(item.category_name)} px-2 py-1 rounded text-xs">${item.category_name}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-xs text-gray-600">
                                    <i class="fas fa-map-marker-alt mr-1"></i>${item.city_name}
                                </span>
                                <span class="text-xs text-gray-500">ID: ${item.article_id}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 按时间分组渲染历史记录
        function renderHistoryByGroup() {
            const container = document.getElementById('historyContainer');
            const loadingState = document.getElementById('loadingState');
            const emptyState = document.getElementById('emptyState');
            const noResultState = document.getElementById('noResultState');

            // 隐藏加载状态
            loadingState.style.display = 'none';

            if (filteredHistory.length === 0) {
                const isSearching = document.getElementById('searchInput').value.trim() !== '';
                if (isSearching) {
                    noResultState.style.display = 'block';
                    emptyState.style.display = 'none';
                } else {
                    emptyState.style.display = 'block';
                    noResultState.style.display = 'none';
                }
                container.innerHTML = '';
                return;
            }

            // 隐藏空状态
            emptyState.style.display = 'none';
            noResultState.style.display = 'none';

            // 按时间分组
            const groups = {};
            filteredHistory.forEach(item => {
                const timeInfo = formatTime(item.browse_time);
                if (!groups[timeInfo.group]) {
                    groups[timeInfo.group] = [];
                }
                groups[timeInfo.group].push(item);
            });

            // 按组顺序排列
            const groupOrder = ['今天', '昨天', '本周', '更早'];
            let html = '';

            groupOrder.forEach(groupName => {
                if (groups[groupName] && groups[groupName].length > 0) {
                    html += `
                        <div class="mb-4">
                            <div class="flex items-center justify-between py-2 border-b border-gray-100">
                                <h3 class="text-sm font-semibold text-gray-800">${groupName}</h3>
                                <span class="text-xs text-gray-500">${groups[groupName].length}条记录</span>
                            </div>
                            <div class="space-y-3 mt-3">
                                ${groups[groupName].map(item => renderHistoryItem(item)).join('')}
                            </div>
                        </div>
                    `;
                }
            });

            container.innerHTML = html;
            updateStatistics();
        }

        // 更新统计信息
        function updateStatistics() {
            const today = new Date();
            const todayStr = today.toDateString();

            const todayCount = browseHistoryData.filter(item => {
                const itemDate = new Date(item.browse_time);
                return itemDate.toDateString() === todayStr;
            }).length;

            const totalCount = browseHistoryData.length;

            // 获取不同分类的数量作为"已收藏"的替代显示
            const categoryCount = new Set(browseHistoryData.map(item => item.category_name)).size;

            document.getElementById('todayCount').textContent = todayCount;
            document.getElementById('totalCount').textContent = totalCount;
            document.getElementById('favoriteCount').textContent = categoryCount;

            // 更新标签文字
            document.querySelector('#favoriteCount').nextElementSibling.textContent = '分类数';
        }

        // 搜索功能
        function searchHistory(keyword) {
            if (!keyword.trim()) {
                filteredHistory = [...browseHistoryData];
            } else {
                filteredHistory = browseHistoryData.filter(item =>
                    item.article_title.toLowerCase().includes(keyword.toLowerCase()) ||
                    item.city_name.toLowerCase().includes(keyword.toLowerCase()) ||
                    item.category_name.toLowerCase().includes(keyword.toLowerCase()) ||
                    item.article_id.toString().includes(keyword)
                );
            }
            renderHistoryByGroup();
        }

        // 切换编辑模式
        function toggleEditMode() {
            isEditMode = !isEditMode;
            const editBtn = document.getElementById('editBtn');
            const container = document.getElementById('historyContainer');

            if (isEditMode) {
                editBtn.textContent = '完成';
                container.classList.add('edit-mode');
            } else {
                editBtn.textContent = '编辑';
                container.classList.remove('edit-mode');
            }
        }

        // 删除历史记录项
        function deleteHistoryItem(id) {
            const item = document.querySelector(`[data-id="${id}"]`);
            if (item) {
                item.classList.add('removing');
                setTimeout(() => {
                    // 从数据中删除
                    const index = browseHistoryData.findIndex(h => h.id === id);
                    if (index > -1) {
                        browseHistoryData.splice(index, 1);
                    }
                    // 重新渲染
                    const keyword = document.getElementById('searchInput').value.trim();
                    searchHistory(keyword);

                    // 这里应该调用API删除服务器端数据
                    // deleteBrowseHistoryAPI(id);
                }, 300);
            }
        }

        // 清空所有记录
        function clearAllHistory() {
            if (confirm('确定要清空所有浏览记录吗？此操作不可恢复。')) {
                browseHistoryData.length = 0;
                filteredHistory.length = 0;
                renderHistoryByGroup();

                // 这里应该调用API清空服务器端数据
                // clearAllBrowseHistoryAPI();
            }
        }

        // 查看详情
        function viewDetail(articleId) {
            if (isEditMode) return;
            alert(`查看招标详情，文章ID: ${articleId}`);
            // 这里可以跳转到详情页面
            // window.location.href = `detail.html?id=${articleId}`;
        }

        // 返回
        function goBack() {
            alert('返回上一页');
            // window.history.back();
        }

        // 获取浏览记录数据
        async function fetchBrowseHistory() {
            try {
                // 模拟API调用
                // const response = await fetch('/api/browse-history');
                // const data = await response.json();

                // 使用模拟数据
                const data = mockApiResponse;

                if (data.code === 0) {
                    browseHistoryData = data.data.list;
                    filteredHistory = [...browseHistoryData];
                    renderHistoryByGroup();
                } else {
                    throw new Error('获取数据失败');
                }
            } catch (error) {
                console.error('获取浏览记录失败:', error);
                document.getElementById('loadingState').innerHTML = `
                    <i class="fas fa-exclamation-triangle text-red-400 text-2xl mb-2"></i>
                    <p class="text-red-500 text-sm">加载失败，请重试</p>
                `;
            }
        }

        // API调用函数（示例）
        async function deleteBrowseHistoryAPI(id) {
            try {
                // const response = await fetch(`/api/browse-history/${id}`, {
                //     method: 'DELETE'
                // });
                // return await response.json();
                console.log(`删除浏览记录 ID: ${id}`);
            } catch (error) {
                console.error('删除失败:', error);
            }
        }

        async function clearAllBrowseHistoryAPI() {
            try {
                // const response = await fetch('/api/browse-history/clear', {
                //     method: 'POST'
                // });
                // return await response.json();
                console.log('清空所有浏览记录');
            } catch (error) {
                console.error('清空失败:', error);
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 获取浏览记录数据
            fetchBrowseHistory();

            // 搜索框事件
            const searchInput = document.getElementById('searchInput');
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    searchHistory(this.value);
                }, 300);
            });
        });
    </script>
</body>
</html>
