<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招标详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            width: 375px;
            height: 812px;
            overflow: hidden;
        }
        .phone-container {
            width: 375px;
            height: 812px;
            overflow-y: auto;
            position: relative;
            background: white;
        }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .mini-capsule {
            position: absolute;
            top: 8px;
            right: 12px;
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .content-section { border-bottom: 1px solid #f3f4f6; }
        .vip-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .vip-modal-content {
            background: white;
            border-radius: 20px;
            padding: 30px 20px;
            margin: 20px;
            text-align: center;
            max-width: 320px;
            width: 100%;
            animation: modalSlideIn 0.3s ease-out;
        }
        @keyframes modalSlideIn {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        .vip-crown {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 小程序胶囊按钮 -->
        <div class="mini-capsule">
            <i class="fas fa-ellipsis-h text-gray-600 text-xs"></i>
        </div>
        
        <!-- 顶部导航 -->
        <div class="gradient-bg px-4 pt-12 pb-4">
            <div class="flex items-center space-x-3">
                <button class="text-white">
                    <i class="fas fa-arrow-left text-lg"></i>
                </button>
                <h1 class="text-white text-lg font-semibold flex-1">招标详情</h1>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="bg-white flex-1 overflow-y-auto">
            <!-- 标题区域 -->
            <div class="px-4 py-4 content-section">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex space-x-2">
                        <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded-md text-xs font-medium">装修工程</span>
                        <span class="bg-green-100 text-green-600 px-2 py-1 rounded-md text-xs font-medium">竞争性磋商</span>
                    </div>
                    <span class="text-xs text-gray-400">今日发布</span>
                </div>
                <h2 class="text-lg font-bold text-gray-800 mb-3 leading-relaxed">中国银行周口分行办公楼15层大会议室装修改造工程</h2>
                <div class="flex items-center space-x-4 text-sm text-gray-600">
                    <div class="flex items-center">
                        <i class="fas fa-eye w-4 mr-1"></i>
                        <span>856</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-building w-4 mr-1"></i>
                        <span>中国银行</span>
                    </div>
                </div>
            </div>

            <!-- 基本信息 -->
            <div class="px-4 py-4 content-section">
                <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                    基本信息
                </h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">采购编号</span>
                        <span class="text-sm font-medium text-gray-800">XDEC-A20250714</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">采购方式</span>
                        <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs">竞争性磋商</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">项目地点</span>
                        <span class="text-sm font-medium text-gray-800">周口市川汇区八一路中段58号</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">计划工期</span>
                        <span class="text-sm font-semibold text-blue-600">50日历天</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">文件发售</span>
                        <span class="text-sm font-medium text-gray-800">2025-07-08 至 2025-07-14</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">开标时间</span>
                        <span class="text-sm font-medium text-red-600">2025-07-21 10:00</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">踏勘时间</span>
                        <span class="text-sm font-medium text-gray-800">2025-07-15 10:00</span>
                    </div>
                </div>
            </div>

            <!-- 项目概况 -->
            <div class="px-4 py-4 content-section">
                <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-file-alt text-purple-500 mr-2"></i>
                    项目概况
                </h3>
                <div class="text-sm text-gray-700 leading-relaxed space-y-3">
                    <div class="bg-blue-50 border-l-4 border-blue-400 p-3 rounded">
                        <p class="font-medium text-blue-800 mb-2">招标范围：</p>
                        <p class="text-blue-700">本工程装修性质为整体装修工程。本工程的施工范围为室内六面体装饰及电气工程。</p>
                    </div>
                    <div class="bg-green-50 border-l-4 border-green-400 p-3 rounded">
                        <p class="font-medium text-green-800 mb-2">质量要求：</p>
                        <p class="text-green-700">达到国家颁布的建设工程施工质量验收合格标准。</p>
                    </div>
                    <div class="bg-purple-50 border-l-4 border-purple-400 p-3 rounded">
                        <p class="font-medium text-purple-800 mb-2">质保期：</p>
                        <p class="text-purple-700">装修2年，其余部分按国家标准执行。</p>
                    </div>
                    <div class="bg-orange-50 border-l-4 border-orange-400 p-3 rounded">
                        <p class="font-medium text-orange-800 mb-2">重要说明：</p>
                        <p class="text-orange-700">本项目不属于依法必须进行招标的项目，也不属于政府采购项目，为采购人自行采购的项目。</p>
                    </div>
                </div>
            </div>

            <!-- 资质要求 -->
            <div class="px-4 py-4 content-section">
                <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-clipboard-check text-orange-500 mr-2"></i>
                    资质要求
                </h3>
                <div class="text-sm text-gray-700 leading-relaxed space-y-3">
                    <div class="bg-red-50 border-l-4 border-red-400 p-3 rounded">
                        <p class="font-medium text-red-800 mb-2">基本资质要求：</p>
                        <ul class="list-disc list-inside space-y-1 text-red-700 text-xs">
                            <li>具备建设主管部门颁发的建筑装修装饰工程专业承包二级及以上资质</li>
                            <li>具有有效的安全生产许可证</li>
                            <li>项目经理应具备相关专业贰级及以上注册建造师证资格</li>
                            <li>提供企业2024年7月以来任意三个月的依法缴纳的税收及社保证明</li>
                        </ul>
                    </div>
                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-3 rounded">
                        <p class="font-medium text-yellow-800 mb-2">信用要求：</p>
                        <ul class="list-disc list-inside space-y-1 text-yellow-700 text-xs">
                            <li>未被"信用中国"网站列入失信被执行人名单</li>
                            <li>近3年经营活动中没有重大违法违规记录</li>
                            <li>未处于中国银行供应商不良行为禁止准入处罚期内</li>
                        </ul>
                    </div>
                    <div class="bg-blue-50 border-l-4 border-blue-400 p-3 rounded">
                        <p class="font-medium text-blue-800 mb-2">其他要求：</p>
                        <ul class="list-disc list-inside space-y-1 text-blue-700 text-xs">
                            <li>本项目不接受联合体</li>
                            <li>供应商不得将本项目采购内容以任何方式进行分包或转包</li>
                            <li>存在关联关系的不同供应商，不得同时参加本项目</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 文件获取 -->
            <div class="px-4 py-4 content-section">
                <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-file-download text-indigo-500 mr-2"></i>
                    文件获取
                </h3>
                <div class="space-y-3">
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 border border-blue-200">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-blue-800">获取方式</span>
                            <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded text-xs">现场获取</span>
                        </div>
                        <div class="text-xs text-blue-700 space-y-1">
                            <p><strong>地址：</strong>河南省国家大学科技园东区15号楼F座西单元河南兴达工程咨询有限公司一楼</p>
                            <p><strong>详细：</strong>郑州市电厂路泾河路向西200米路南</p>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-3 border border-green-200">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-green-800">售价信息</span>
                            <span class="bg-green-100 text-green-600 px-2 py-1 rounded text-xs">售后不退</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-green-700">招标文件：500元</span>
                            <span class="text-xs text-green-700">图纸费：100元</span>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-3 border border-orange-200">
                        <div class="text-sm font-medium text-orange-800 mb-2">注册要求</div>
                        <div class="text-xs text-orange-700 space-y-1">
                            <p>• 需在中国银行中银智采平台完成注册</p>
                            <p>• 平台地址：ctpch.fmscop.bankofchina.com</p>
                            <p>• 请在文件领取截止日前1工作日内完成注册</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 联系方式 -->
            <div class="px-4 py-4 content-section">
                <h3 class="text-sm font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fas fa-phone text-green-500 mr-2"></i>
                    联系方式
                </h3>
                <div class="space-y-3">
                    <div class="bg-gray-50 rounded-lg p-3">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-800">招标人</span>
                            <span class="text-sm text-gray-600">中国银行股份有限公司周口分行</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm text-gray-600">联系人</span>
                            <span class="text-sm text-gray-800">郭先生</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm text-gray-600">联系电话</span>
                            <a href="tel:0394-8109045" class="text-sm text-blue-600">0394-8109045</a>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">地址</span>
                            <span class="text-xs text-gray-600">周口市川汇区八一路中段58号</span>
                        </div>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-3">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-800">招标代理机构</span>
                            <span class="text-sm text-gray-600">河南兴达工程咨询有限公司</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm text-gray-600">联系人</span>
                            <span class="text-sm text-gray-800">王鹏勇、张瑞瑞</span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm text-gray-600">联系电话</span>
                            <a href="tel:0371-68850950" class="text-sm text-blue-600">0371-68850950</a>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">邮箱</span>
                            <a href="mailto:<EMAIL>" class="text-xs text-blue-600"><EMAIL></a>
                        </div>
                    </div>
                    <div class="bg-red-50 rounded-lg p-3 border border-red-200">
                        <div class="text-sm font-medium text-red-800 mb-2">踏勘安排</div>
                        <div class="text-xs text-red-700 space-y-1">
                            <p><strong>时间：</strong>2025年7月15日上午10:00</p>
                            <p><strong>联系人：</strong>赵老师</p>
                            <p><strong>电话：</strong><a href="tel:18603879107" class="text-blue-600">18603879107</a></p>
                            <p><strong>地点：</strong>周口市川汇区八一路中段58号中国银行周口分行</p>
                        </div>
                    </div>
                </div>
            </div>

        <!-- 底部操作栏 -->
        <div class="bg-white border-t border-gray-200 px-4 py-3">
            <button class="w-full bg-gradient-to-r from-purple-500 to-blue-500 text-white py-3 rounded-lg text-sm font-medium" onclick="checkVipStatus()">
                获取招标文件
            </button>
        </div>
        </div>

        <!-- VIP弹窗 -->
        <div id="vipModal" class="vip-modal" style="display: none;">
            <div class="vip-modal-content">
                <div class="vip-crown">
                    <i class="fas fa-crown text-white text-3xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-3">开通VIP会员</h3>
                <p class="text-gray-600 text-sm mb-6 leading-relaxed">
                    查看完整招标详情需要开通VIP会员<br>
                    解锁全部功能，获取更多商机
                </p>

                <!-- VIP特权展示 -->
                <div class="grid grid-cols-2 gap-3 mb-6">
                    <div class="bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-3 text-center">
                        <i class="fas fa-eye text-blue-500 text-lg mb-1"></i>
                        <p class="text-xs text-gray-700">查看详情</p>
                    </div>
                    <div class="bg-gradient-to-br from-green-50 to-blue-50 rounded-lg p-3 text-center">
                        <i class="fas fa-download text-green-500 text-lg mb-1"></i>
                        <p class="text-xs text-gray-700">下载文档</p>
                    </div>
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg p-3 text-center">
                        <i class="fas fa-bell text-purple-500 text-lg mb-1"></i>
                        <p class="text-xs text-gray-700">实时推送</p>
                    </div>
                    <div class="bg-gradient-to-br from-orange-50 to-red-50 rounded-lg p-3 text-center">
                        <i class="fas fa-search-plus text-orange-500 text-lg mb-1"></i>
                        <p class="text-xs text-gray-700">高级搜索</p>
                    </div>
                </div>

                <!-- 价格信息 -->
                <div class="bg-gradient-to-r from-purple-100 to-blue-100 rounded-lg p-4 mb-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">年度会员</p>
                            <p class="text-xs text-gray-500">最超值选择</p>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-purple-600">¥798</p>
                            <p class="text-xs text-gray-500 line-through">¥1188</p>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <button class="w-full bg-gradient-to-r from-purple-500 to-blue-500 text-white py-3 rounded-lg text-sm font-medium mb-3" onclick="goToMemberPage()">
                    立即开通VIP会员
                </button>
                <p class="text-xs text-gray-500">开通即可查看完整招标信息</p>
            </div>
        </div>

        <!-- 登录弹窗 -->
        <div id="loginModal" class="vip-modal" style="display: none;">
            <div class="vip-modal-content">
                <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-user text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-3">请先登录</h3>
                <p class="text-gray-600 text-sm mb-6 leading-relaxed">
                    登录后即可查看招标详情<br>
                    享受更多专业服务
                </p>

                <!-- 登录优势 -->
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 mb-6">
                    <div class="space-y-2">
                        <div class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>免费查看基础信息</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>保存浏览记录</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-700">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>个性化推荐</span>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <button class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-lg text-sm font-medium mb-3" onclick="goToLogin()">
                    立即登录
                </button>
                <p class="text-xs text-gray-500">登录即可享受更多服务</p>
            </div>
        </div>
    </div>

    <script>
        // 检查用户状态并显示相应弹窗（点击获取文件按钮时调用）
        function checkVipStatus() {
            // 模拟用户状态检查
            const isLoggedIn = false; // 假设已登录（因为能进入详情页）
            const isVipMember = false; // 模拟非VIP用户

            if (!isLoggedIn) {
                // 未登录，显示登录弹窗
                showLoginModal();
            } else {
                // 已登录，验证是否为VIP会员
                if (isVipMember) {
                    // 已是会员，正常执行获取文件操作
                    alert('VIP用户，可以获取招标文件！');
                } else {
                    // 不是会员，显示购买会员弹窗
                    showVipModal();
                }
            }
        }

        function showVipModal() {
            document.getElementById('vipModal').style.display = 'flex';
            // 禁止页面滚动
            document.body.style.overflow = 'hidden';
        }

        function showLoginModal() {
            document.getElementById('loginModal').style.display = 'flex';
            // 禁止页面滚动
            document.body.style.overflow = 'hidden';
        }

        function goToMemberPage() {
            // 跳转到会员开通页面
            window.location.href = 'member.html';
        }

        function goToLogin() {
            // 跳转到登录页面
            alert('跳转到登录页面');
            // window.location.href = 'login.html';
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            // 页面加载时检查用户状态
            checkUserStatusOnLoad();
        };

        // 页面加载时检查用户状态
        function checkUserStatusOnLoad() {
            const isLoggedIn = true; // 假设已登录（因为能进入详情页）
            const isVipMember = true; // 模拟非VIP用户

            if (!isLoggedIn) {
                // 未登录，显示登录弹窗
                showLoginModal();
            } else if (!isVipMember) {
                // 已登录但不是VIP会员，显示VIP弹窗
                showVipModal();
            }
            // 如果是VIP会员，则正常显示页面内容
        };

        // 阻止弹窗外部点击关闭
        document.getElementById('vipModal').addEventListener('click', function(e) {
            e.stopPropagation();
        });

        document.getElementById('loginModal').addEventListener('click', function(e) {
            e.stopPropagation();
        });
    </script>
</body>
</html>
