-- =============================================
-- 简化版热门搜索数据库设计
-- 创建时间: 2025-07-15
-- 描述: 简单实用的热门搜索功能
-- =============================================

USE bidding_system;

-- =============================================
-- 1. 搜索关键词表（简化版）
-- =============================================
CREATE TABLE search_keywords_simple (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '关键词ID',
    keyword VARCHAR(100) NOT NULL UNIQUE COMMENT '搜索关键词',
    search_count INT DEFAULT 0 COMMENT '总搜索次数',
    trend ENUM('up', 'down', 'stable') DEFAULT 'stable' COMMENT '趋势',
    is_hot BOOLEAN DEFAULT FALSE COMMENT '是否热门',
    is_new BOOLEAN DEFAULT FALSE COMMENT '是否新词',
    last_search_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后搜索时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_search_count (search_count DESC),
    INDEX idx_is_hot (is_hot),
    INDEX idx_last_search_time (last_search_time)
) COMMENT '搜索关键词表';

-- =============================================
-- 2. 插入示例数据
-- =============================================
INSERT INTO search_keywords_simple (keyword, search_count, trend, is_hot, is_new) VALUES
('智慧城市建设', 1250, 'up', TRUE, FALSE),
('医院建设', 980, 'up', TRUE, FALSE),
('5G基站', 856, 'stable', TRUE, FALSE),
('教育装备', 742, 'down', TRUE, FALSE),
('环保工程', 698, 'up', TRUE, FALSE),
('数据中心', 634, 'up', TRUE, FALSE),
('新能源', 587, 'stable', TRUE, FALSE),
('公路建设', 523, 'down', TRUE, FALSE),
('政府采购', 456, 'stable', FALSE, FALSE),
('信息系统', 398, 'up', FALSE, FALSE),
('人工智能', 234, 'up', FALSE, TRUE),
('区块链', 187, 'up', FALSE, TRUE);

-- =============================================
-- 3. 简单的操作函数
-- =============================================

-- 记录搜索（简化版）
DELIMITER //
CREATE PROCEDURE RecordSearchSimple(IN p_keyword VARCHAR(100))
BEGIN
    INSERT INTO search_keywords_simple (keyword, search_count) 
    VALUES (p_keyword, 1)
    ON DUPLICATE KEY UPDATE 
        search_count = search_count + 1,
        last_search_time = NOW();
END //
DELIMITER ;

-- 获取热门搜索
DELIMITER //
CREATE PROCEDURE GetHotSearchSimple(IN p_limit INT)
BEGIN
    SELECT 
        keyword,
        search_count,
        trend,
        is_hot,
        is_new
    FROM search_keywords_simple 
    WHERE is_hot = TRUE OR is_new = TRUE
    ORDER BY is_hot DESC, search_count DESC
    LIMIT p_limit;
END //
DELIMITER ;

-- 手动更新热门状态（可以定期执行）
DELIMITER //
CREATE PROCEDURE UpdateHotStatusSimple()
BEGIN
    -- 重置热门状态
    UPDATE search_keywords_simple SET is_hot = FALSE;
    
    -- 设置前8个为热门
    UPDATE search_keywords_simple 
    SET is_hot = TRUE 
    WHERE id IN (
        SELECT id FROM (
            SELECT id FROM search_keywords_simple 
            ORDER BY search_count DESC 
            LIMIT 8
        ) tmp
    );
    
    -- 设置最近创建的4个为新词
    UPDATE search_keywords_simple SET is_new = FALSE;
    UPDATE search_keywords_simple 
    SET is_new = TRUE 
    WHERE id IN (
        SELECT id FROM (
            SELECT id FROM search_keywords_simple 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            ORDER BY search_count DESC 
            LIMIT 4
        ) tmp
    );
END //
DELIMITER ;

-- =============================================
-- 使用说明
-- =============================================

/*
## 简化版使用方法

### 1. 用户搜索时记录
```sql
CALL RecordSearchSimple('智慧城市建设');
```

### 2. 获取热门搜索数据
```sql
CALL GetHotSearchSimple(20);
```

### 3. 定期更新热门状态（可选，比如每天执行一次）
```sql
CALL UpdateHotStatusSimple();
```

### 4. 手动添加关键词
```sql
INSERT INTO search_keywords_simple (keyword, search_count, is_hot) 
VALUES ('新关键词', 100, TRUE);
```

### 5. 手动设置热门
```sql
UPDATE search_keywords_simple 
SET is_hot = TRUE 
WHERE keyword = '特定关键词';
```

### 6. 查看所有数据
```sql
SELECT * FROM search_keywords_simple 
ORDER BY search_count DESC;
```

## 优点
- 只有1个表，结构简单
- 只有3个存储过程，易于维护
- 可以手动管理，也可以自动统计
- 满足基本的热门搜索需求

## 如果不想用数据库
也可以直接在代码中写死热门搜索数据：

```javascript
const hotSearchData = [
    {
        "keyword": "智慧城市建设",
        "search_count": 1250,
        "trend": "up",
        "is_hot": true,
        "is_new": false
    },
    {
        "keyword": "医院建设",
        "search_count": 980,
        "trend": "up",
        "is_hot": true,
        "is_new": false
    }
    // ... 更多数据
];
```

这样连数据库都不需要了！
*/
