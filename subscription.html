<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的订阅</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            width: 375px;
            height: 812px;
            overflow: hidden;
        }
        .phone-container {
            width: 375px;
            height: 812px;
            overflow-y: auto;
            position: relative;
            background: white;
        }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-shadow { box-shadow: 0 4px 20px rgba(0,0,0,0.08); }
        .mini-capsule {
            position: absolute;
            top: 8px;
            right: 12px;
            width: 24px;
            height: 24px;
            background: rgba(255,255,255,0.9);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .subscription-item { 
            transition: transform 0.2s ease; 
            cursor: pointer;
        }
        .subscription-item:hover { transform: translateY(-2px); }
        .status-active { 
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .status-expired { 
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
        .status-expiring { 
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
        .city-tag {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            margin: 2px;
        }
        .progress-bar {
            height: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: rgba(255,255,255,0.8);
            border-radius: 2px;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <!-- 小程序胶囊按钮 -->
        <div class="mini-capsule">
            <i class="fas fa-ellipsis-h text-gray-600 text-xs"></i>
        </div>
        
        <!-- 顶部导航 -->
        <div class="gradient-bg px-4 pt-12 pb-4">
            <div class="flex items-center space-x-3">
                <button class="text-white" onclick="goBack()">
                    <i class="fas fa-arrow-left text-lg"></i>
                </button>
                <h1 class="text-white text-lg font-semibold flex-1">我的订阅</h1>
                <button class="text-white text-sm" onclick="refreshSubscriptions()">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="bg-white flex-1 overflow-y-auto">
            <!-- 统计信息 -->
            <div class="px-4 py-4 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-6">
                        <div class="text-center">
                            <div class="text-lg font-bold text-green-600" id="activeCount">0</div>
                            <div class="text-xs text-gray-600">生效中</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-orange-600" id="expiringCount">0</div>
                            <div class="text-xs text-gray-600">即将到期</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-blue-600" id="totalCities">0</div>
                            <div class="text-xs text-gray-600">覆盖城市</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 订阅列表 -->
            <div class="px-4 py-2" id="subscriptionContainer">
                <!-- 加载状态 -->
                <div id="loadingState" class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-2"></i>
                    <p class="text-gray-500 text-sm">加载中...</p>
                </div>
                
                <!-- 空状态 -->
                <div id="emptyState" class="text-center py-12" style="display: none;">
                    <i class="fas fa-bell-slash text-gray-300 text-4xl mb-4"></i>
                    <p class="text-gray-500 text-sm mb-2">暂无订阅服务</p>
                    <p class="text-gray-400 text-xs">购买会员后即可享受订阅服务</p>
                    <button class="mt-4 bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-2 rounded-full text-sm font-medium" onclick="goToPurchase()">
                        立即购买
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟API返回的订阅数据（基于数据库表结构）
        const mockSubscriptionData = {
            "code": 0,
            "data": {
                "list": [
                    {
                        "id": 1,
                        "order_sn": "ZB202507251114227737",
                        "good_id": 2,
                        "good_name": "特惠会员399元",
                        "city_count": 2,
                        "user_id": 1,
                        "good_price": 298.00,
                        "effective": 2,
                        "price": 596.00,
                        "amount": 596.00,
                        "pay_status": 1,
                        "remark": "选择城市：内蒙古、北京",
                        "pay_at": "2025-07-25T15:37:35Z",
                        "created_at": "2025-07-25T11:14:22Z",
                        "updated_at": "2025-07-25T15:37:35Z",
                        "cities": [
                            {"city_id": 29, "city_name": "内蒙古"},
                            {"city_id": 1, "city_name": "北京"}
                        ]
                        // 注意：expire_date 和 days_remaining 应该由前端计算
                    },
                    {
                        "id": 3,
                        "order_sn": "ZB202507261037064862",
                        "good_id": 2,
                        "good_name": "特惠会员399元",
                        "city_count": 1,
                        "user_id": 1,
                        "good_price": 298.00,
                        "effective": 2,
                        "price": 298.00,
                        "amount": 298.00,
                        "pay_status": 1,
                        "remark": "选择城市：北京",
                        "pay_at": "2025-07-26T10:37:33Z",
                        "created_at": "2025-07-26T10:37:07Z",
                        "updated_at": "2025-07-26T10:37:33Z",
                        "cities": [
                            {"city_id": 1, "city_name": "北京"}
                        ]
                    },
                    {
                        "id": 4,
                        "order_sn": "ZB202507261102111862",
                        "good_id": 2,
                        "good_name": "特惠会员399元",
                        "city_count": 32,
                        "user_id": 1,
                        "good_price": 298.00,
                        "effective": 2,
                        "price": 9536.00,
                        "amount": 9536.00,
                        "pay_status": 1,
                        "remark": "选择城市：全国32个省市",
                        "pay_at": "2025-07-28T11:54:04Z",
                        "created_at": "2025-07-26T11:02:11Z",
                        "updated_at": "2025-07-28T11:54:04Z",
                        "cities": [
                            {"city_id": 1, "city_name": "北京"}, {"city_id": 2, "city_name": "浙江"}, 
                            {"city_id": 3, "city_name": "天津"}, {"city_id": 4, "city_name": "安徽"},
                            {"city_id": 5, "city_name": "上海"}, {"city_id": 6, "city_name": "福建"},
                            {"city_id": 7, "city_name": "重庆"}, {"city_id": 8, "city_name": "江西"},
                            {"city_id": 9, "city_name": "山东"}, {"city_id": 10, "city_name": "河南"},
                            {"city_id": 11, "city_name": "湖北"}, {"city_id": 12, "city_name": "湖南"},
                            {"city_id": 13, "city_name": "广东"}, {"city_id": 14, "city_name": "海南"},
                            {"city_id": 15, "city_name": "山西"}, {"city_id": 16, "city_name": "青海"},
                            {"city_id": 17, "city_name": "江苏"}, {"city_id": 18, "city_name": "辽宁"},
                            {"city_id": 19, "city_name": "吉林"}, {"city_id": 20, "city_name": "河北"},
                            {"city_id": 21, "city_name": "贵州"}, {"city_id": 22, "city_name": "四川"},
                            {"city_id": 23, "city_name": "云南"}, {"city_id": 24, "city_name": "陕西"},
                            {"city_id": 25, "city_name": "黑龙江"}, {"city_id": 26, "city_name": "广西"},
                            {"city_id": 27, "city_name": "宁夏"}, {"city_id": 28, "city_name": "新疆"},
                            {"city_id": 29, "city_name": "内蒙古"}, {"city_id": 30, "city_name": "西藏"},
                            {"city_id": 31, "city_name": "香港"}, {"city_id": 32, "city_name": "澳门"}
                        ]
                    }
                ],
                "total": 3
            }
        };

        // 全局变量
        let subscriptionData = [];

        // 计算到期日期
        function calculateExpireDate(payAt, effectiveMonths) {
            const payDate = new Date(payAt);
            const expireDate = new Date(payDate);
            expireDate.setMonth(expireDate.getMonth() + effectiveMonths);
            return expireDate;
        }

        // 计算剩余天数
        function calculateDaysRemaining(expireDate) {
            const now = new Date();
            const expire = new Date(expireDate);
            const diffTime = expire - now;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return Math.max(0, diffDays); // 不能为负数
        }

        // 计算订阅状态
        function getSubscriptionStatus(daysRemaining) {
            if (daysRemaining <= 0) {
                return { status: 'expired', text: '已过期', class: 'status-expired' };
            } else if (daysRemaining <= 7) {
                return { status: 'expiring', text: '即将到期', class: 'status-expiring' };
            } else {
                return { status: 'active', text: '生效中', class: 'status-active' };
            }
        }

        // 处理订阅数据，添加计算字段
        function processSubscriptionData(rawData) {
            return rawData.map(item => {
                // 计算到期日期
                const expireDate = calculateExpireDate(item.pay_at, item.effective);
                // 计算剩余天数
                const daysRemaining = calculateDaysRemaining(expireDate);

                return {
                    ...item,
                    expire_date: expireDate.toISOString(),
                    days_remaining: daysRemaining
                };
            });
        }

        // 格式化城市显示
        function formatCityDisplay(cities, maxShow = 6) {
            if (cities.length <= maxShow) {
                return cities.map(city => city.city_name).join('、');
            }
            return cities.slice(0, maxShow).map(city => city.city_name).join('、') + ` 等${cities.length}个`;
        }

        // 格式化时间
        function formatDate(dateStr) {
            const date = new Date(dateStr);
            return date.toLocaleDateString('zh-CN', { 
                year: 'numeric', 
                month: '2-digit', 
                day: '2-digit' 
            });
        }

        // 计算进度百分比
        function calculateProgress(daysRemaining, totalDays) {
            if (totalDays <= 0) return 0;
            const progress = Math.max(0, Math.min(100, (daysRemaining / totalDays) * 100));
            return progress;
        }

        // 渲染订阅项
        function renderSubscriptionItem(item) {
            const statusInfo = getSubscriptionStatus(item.days_remaining);
            const cityDisplay = formatCityDisplay(item.cities);
            const expireDate = formatDate(item.expire_date);
            const progress = calculateProgress(item.days_remaining, item.effective * 30); // 假设按月计算

            return `
                <div class="subscription-item bg-white rounded-xl p-4 card-shadow border border-gray-100 mb-3" onclick="viewSubscriptionDetail('${item.order_sn}')">
                    <!-- 订阅头部 -->
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <h4 class="text-sm font-semibold text-gray-800">${item.good_name}</h4>
                                <span class="${statusInfo.class} text-white px-2 py-1 rounded-full text-xs font-medium">
                                    ${statusInfo.text}
                                </span>
                            </div>
                            <p class="text-xs text-gray-500">订单号：${item.order_sn}</p>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-purple-600">¥${item.amount}</div>
                            <div class="text-xs text-gray-500">${item.city_count}个城市</div>
                        </div>
                    </div>

                    <!-- 时间信息 -->
                    <div class="bg-gray-50 rounded-lg p-3 mb-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-xs text-gray-600">有效期至</span>
                            <span class="text-xs font-medium text-gray-800">${expireDate}</span>
                        </div>
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-xs text-gray-600">剩余天数</span>
                            <span class="text-xs font-medium ${item.days_remaining <= 7 ? 'text-orange-600' : 'text-green-600'}">
                                ${item.days_remaining}天
                            </span>
                        </div>
                        <!-- 进度条 -->
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${progress}%"></div>
                        </div>
                    </div>

                    <!-- 城市信息 -->
                    <div class="mb-3">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-xs text-gray-600">覆盖城市</span>
                            ${item.cities.length > 6 ? `
                            <button class="text-xs text-blue-600 underline" onclick="showAllCities(event, '${item.order_sn}', ${JSON.stringify(item.cities).replace(/"/g, '&quot;')})">
                                查看全部
                            </button>
                            ` : ''}
                        </div>
                        <p class="text-xs text-gray-700 leading-relaxed">${cityDisplay}</p>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex items-center space-x-2">
                        <button class="flex-1 bg-gray-100 text-gray-600 py-2 rounded-lg text-xs font-medium" onclick="manageSubscription(event, '${item.order_sn}')">
                            管理订阅
                        </button>
                        ${item.days_remaining <= 30 ? `
                        <button class="flex-1 bg-gradient-to-r from-green-500 to-blue-500 text-white py-2 rounded-lg text-xs font-medium" onclick="renewSubscription(event, '${item.order_sn}')">
                            续费
                        </button>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // 渲染订阅列表
        function renderSubscriptionList(subscriptions) {
            const container = document.getElementById('subscriptionContainer');
            const loadingState = document.getElementById('loadingState');
            const emptyState = document.getElementById('emptyState');

            // 隐藏加载状态
            loadingState.style.display = 'none';

            if (subscriptions.length === 0) {
                emptyState.style.display = 'block';
                container.innerHTML = '';
                return;
            }

            // 隐藏空状态
            emptyState.style.display = 'none';

            // 按状态排序：生效中 > 即将到期 > 已过期
            const sortedSubscriptions = subscriptions.sort((a, b) => {
                const statusA = getSubscriptionStatus(a.days_remaining).status;
                const statusB = getSubscriptionStatus(b.days_remaining).status;

                const statusOrder = { 'active': 0, 'expiring': 1, 'expired': 2 };
                return statusOrder[statusA] - statusOrder[statusB];
            });

            const html = sortedSubscriptions.map(item => renderSubscriptionItem(item)).join('');
            container.innerHTML = html;

            updateStatistics(subscriptions);
        }

        // 更新统计信息
        function updateStatistics(subscriptions) {
            const activeCount = subscriptions.filter(item =>
                getSubscriptionStatus(item.days_remaining).status === 'active'
            ).length;

            const expiringCount = subscriptions.filter(item =>
                getSubscriptionStatus(item.days_remaining).status === 'expiring'
            ).length;

            const totalCities = new Set(
                subscriptions.flatMap(item => item.cities.map(city => city.city_id))
            ).size;

            document.getElementById('activeCount').textContent = activeCount;
            document.getElementById('expiringCount').textContent = expiringCount;
            document.getElementById('totalCities').textContent = totalCities;
        }

        // 获取订阅数据
        async function fetchSubscriptions() {
            try {
                // 模拟API调用
                // const response = await fetch('/api/subscriptions');
                // const data = await response.json();

                // 使用模拟数据
                const data = mockSubscriptionData;

                if (data.code === 0) {
                    // 处理原始数据，添加计算字段
                    subscriptionData = processSubscriptionData(data.data.list);
                    renderSubscriptionList(subscriptionData);
                } else {
                    throw new Error('获取数据失败');
                }
            } catch (error) {
                console.error('获取订阅数据失败:', error);
                document.getElementById('loadingState').innerHTML = `
                    <i class="fas fa-exclamation-triangle text-red-400 text-2xl mb-2"></i>
                    <p class="text-red-500 text-sm">加载失败，请重试</p>
                `;
            }
        }

        // 显示所有城市
        function showAllCities(event, orderSn, cities) {
            event.stopPropagation();

            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
            modal.innerHTML = `
                <div class="bg-white rounded-xl p-6 max-w-sm w-full max-h-96 overflow-y-auto">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">覆盖城市</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-lg"></i>
                        </button>
                    </div>
                    <div class="mb-4">
                        <p class="text-sm text-gray-600 mb-3">订单号：${orderSn}</p>
                        <p class="text-sm text-blue-600 mb-3">共覆盖 ${cities.length} 个城市</p>
                    </div>
                    <div class="grid grid-cols-2 gap-2">
                        ${cities.map(city => `
                            <div class="bg-blue-50 text-blue-700 px-3 py-2 rounded-lg text-sm text-center">
                                ${city.city_name}
                            </div>
                        `).join('')}
                    </div>
                    <div class="mt-6">
                        <button onclick="this.closest('.fixed').remove()" class="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-2 rounded-lg text-sm font-medium">
                            确定
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        // 查看订阅详情
        function viewSubscriptionDetail(orderSn) {
            alert(`查看订阅详情，订单号: ${orderSn}`);
        }

        // 管理订阅
        function manageSubscription(event, orderSn) {
            event.stopPropagation();
            alert(`管理订阅，订单号: ${orderSn}`);
        }

        // 续费订阅
        function renewSubscription(event, orderSn) {
            event.stopPropagation();
            alert(`续费订阅，订单号: ${orderSn}`);
        }

        // 刷新订阅
        function refreshSubscriptions() {
            document.getElementById('loadingState').style.display = 'block';
            document.getElementById('subscriptionContainer').innerHTML = '<div id="loadingState" class="text-center py-8"><i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-2"></i><p class="text-gray-500 text-sm">加载中...</p></div>';
            setTimeout(() => {
                fetchSubscriptions();
            }, 500);
        }

        // 去购买
        function goToPurchase() {
            alert('跳转到购买页面');
        }

        // 返回
        function goBack() {
            alert('返回上一页');
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            fetchSubscriptions();
        });
    </script>
</body>
</html>
